#!/bin/bash

# =============================================================================
# Processing Apps Migration Validation Script
# =============================================================================

set -euo pipefail

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-nonprod}
REGION=${2:-us-east-1}
APPLICATION="ais"
COMPONENT="processing-apps"

# Resource names
CLUSTER_NAME="${APPLICATION}-${ENVIRONMENT}-${COMPONENT}-ecs"
ASG_NAME="${APPLICATION}-${ENVIRONMENT}-${COMPONENT}-asg"
LAMBDA_NAME="${APPLICATION}_${ENVIRONMENT}_ecs_term_protection"

# Logging functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Validation functions
validate_prerequisites() {
    log_info "Validating prerequisites..."
    
    # Check AWS CLI
    if ! command -v aws &> /dev/null; then
        log_error "AWS CLI is not installed"
        exit 1
    fi
    
    # Check Terraform
    if ! command -v terraform &> /dev/null; then
        log_error "Terraform is not installed"
        exit 1
    fi
    
    # Check AWS credentials
    if ! aws sts get-caller-identity &> /dev/null; then
        log_error "AWS credentials not configured or invalid"
        exit 1
    fi
    
    log_success "Prerequisites validated"
}

validate_ecs_cluster() {
    log_info "Validating ECS cluster: ${CLUSTER_NAME}"
    
    # Check if cluster exists
    if ! aws ecs describe-clusters --clusters "${CLUSTER_NAME}" --region "${REGION}" &> /dev/null; then
        log_error "ECS cluster ${CLUSTER_NAME} not found"
        return 1
    fi
    
    # Get cluster details
    local cluster_status
    cluster_status=$(aws ecs describe-clusters \
        --clusters "${CLUSTER_NAME}" \
        --region "${REGION}" \
        --query 'clusters[0].status' \
        --output text)
    
    if [[ "${cluster_status}" != "ACTIVE" ]]; then
        log_error "ECS cluster ${CLUSTER_NAME} is not active (status: ${cluster_status})"
        return 1
    fi
    
    # Check container insights
    local container_insights
    container_insights=$(aws ecs describe-clusters \
        --clusters "${CLUSTER_NAME}" \
        --region "${REGION}" \
        --include INSIGHTS \
        --query 'clusters[0].settings[?name==`containerInsights`].value' \
        --output text)
    
    if [[ "${container_insights}" == "enabled" ]]; then
        log_success "ECS cluster ${CLUSTER_NAME} is active with Container Insights enabled"
    else
        log_warning "ECS cluster ${CLUSTER_NAME} is active but Container Insights is not enabled"
    fi
}

validate_autoscaling_group() {
    log_info "Validating Auto Scaling Group: ${ASG_NAME}"
    
    # Check if ASG exists
    if ! aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "${ASG_NAME}" \
        --region "${REGION}" &> /dev/null; then
        log_error "Auto Scaling Group ${ASG_NAME} not found"
        return 1
    fi
    
    # Get ASG details
    local asg_info
    asg_info=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "${ASG_NAME}" \
        --region "${REGION}" \
        --query 'AutoScalingGroups[0].[MinSize,DesiredCapacity,MaxSize,Instances[].InstanceId]' \
        --output text)
    
    local min_size desired_capacity max_size instances
    read -r min_size desired_capacity max_size instances <<< "${asg_info}"
    
    log_success "Auto Scaling Group ${ASG_NAME} found"
    log_info "  Min Size: ${min_size}"
    log_info "  Desired Capacity: ${desired_capacity}"
    log_info "  Max Size: ${max_size}"
    log_info "  Current Instances: $(echo "${instances}" | wc -w)"
    
    # Check launch template
    local launch_template
    launch_template=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "${ASG_NAME}" \
        --region "${REGION}" \
        --query 'AutoScalingGroups[0].LaunchTemplate.LaunchTemplateName' \
        --output text)
    
    if [[ "${launch_template}" != "None" ]]; then
        log_success "Auto Scaling Group is using Launch Template: ${launch_template}"
    else
        log_warning "Auto Scaling Group is not using a Launch Template"
    fi
}

validate_lambda_function() {
    log_info "Validating Lambda function: ${LAMBDA_NAME}"
    
    # Check if Lambda function exists
    if ! aws lambda get-function \
        --function-name "${LAMBDA_NAME}" \
        --region "${REGION}" &> /dev/null; then
        log_error "Lambda function ${LAMBDA_NAME} not found"
        return 1
    fi
    
    # Get Lambda details
    local runtime state
    runtime=$(aws lambda get-function \
        --function-name "${LAMBDA_NAME}" \
        --region "${REGION}" \
        --query 'Configuration.Runtime' \
        --output text)
    
    state=$(aws lambda get-function \
        --function-name "${LAMBDA_NAME}" \
        --region "${REGION}" \
        --query 'Configuration.State' \
        --output text)
    
    if [[ "${state}" == "Active" ]]; then
        log_success "Lambda function ${LAMBDA_NAME} is active (runtime: ${runtime})"
    else
        log_error "Lambda function ${LAMBDA_NAME} is not active (state: ${state})"
        return 1
    fi
    
    # Check if function has X-Ray tracing enabled
    local tracing
    tracing=$(aws lambda get-function \
        --function-name "${LAMBDA_NAME}" \
        --region "${REGION}" \
        --query 'Configuration.TracingConfig.Mode' \
        --output text)
    
    if [[ "${tracing}" == "Active" ]]; then
        log_success "Lambda function has X-Ray tracing enabled"
    else
        log_warning "Lambda function does not have X-Ray tracing enabled"
    fi
}

validate_cloudwatch_alarms() {
    log_info "Validating CloudWatch alarms..."
    
    # Get alarms for the cluster
    local alarms
    alarms=$(aws cloudwatch describe-alarms \
        --alarm-name-prefix "${APPLICATION}-${ENVIRONMENT}-${COMPONENT}" \
        --region "${REGION}" \
        --query 'MetricAlarms[].AlarmName' \
        --output text)
    
    if [[ -z "${alarms}" ]]; then
        log_error "No CloudWatch alarms found for ${APPLICATION}-${ENVIRONMENT}-${COMPONENT}"
        return 1
    fi
    
    local alarm_count
    alarm_count=$(echo "${alarms}" | wc -w)
    log_success "Found ${alarm_count} CloudWatch alarms"
    
    # Check alarm states
    local ok_count insufficient_count alarm_count_state
    ok_count=0
    insufficient_count=0
    alarm_count_state=0
    
    for alarm in ${alarms}; do
        local state
        state=$(aws cloudwatch describe-alarms \
            --alarm-names "${alarm}" \
            --region "${REGION}" \
            --query 'MetricAlarms[0].StateValue' \
            --output text)
        
        case "${state}" in
            "OK")
                ((ok_count++))
                ;;
            "INSUFFICIENT_DATA")
                ((insufficient_count++))
                ;;
            "ALARM")
                ((alarm_count_state++))
                log_warning "Alarm ${alarm} is in ALARM state"
                ;;
        esac
    done
    
    log_info "Alarm states: OK=${ok_count}, INSUFFICIENT_DATA=${insufficient_count}, ALARM=${alarm_count_state}"
}

validate_security_groups() {
    log_info "Validating security groups..."
    
    # Get security groups for the ASG instances
    local instance_ids
    instance_ids=$(aws autoscaling describe-auto-scaling-groups \
        --auto-scaling-group-names "${ASG_NAME}" \
        --region "${REGION}" \
        --query 'AutoScalingGroups[0].Instances[].InstanceId' \
        --output text)
    
    if [[ -z "${instance_ids}" ]]; then
        log_warning "No instances found in Auto Scaling Group"
        return 0
    fi
    
    local first_instance
    first_instance=$(echo "${instance_ids}" | awk '{print $1}')
    
    local security_groups
    security_groups=$(aws ec2 describe-instances \
        --instance-ids "${first_instance}" \
        --region "${REGION}" \
        --query 'Reservations[0].Instances[0].SecurityGroups[].GroupId' \
        --output text)
    
    local sg_count
    sg_count=$(echo "${security_groups}" | wc -w)
    log_success "Instances have ${sg_count} security groups attached"
    
    # Check for common security group rules
    for sg in ${security_groups}; do
        local ssh_rule
        ssh_rule=$(aws ec2 describe-security-groups \
            --group-ids "${sg}" \
            --region "${REGION}" \
            --query 'SecurityGroups[0].IpPermissions[?FromPort==`22`]' \
            --output text)
        
        if [[ -n "${ssh_rule}" ]]; then
            log_success "Security group ${sg} has SSH access configured"
        fi
    done
}

validate_log_groups() {
    log_info "Validating CloudWatch log groups..."
    
    # Check for Lambda log group
    local lambda_log_group="/aws/lambda/${LAMBDA_NAME}"
    if aws logs describe-log-groups \
        --log-group-name-prefix "${lambda_log_group}" \
        --region "${REGION}" &> /dev/null; then
        log_success "Lambda log group exists: ${lambda_log_group}"
    else
        log_error "Lambda log group not found: ${lambda_log_group}"
    fi
    
    # Check for common task log groups
    local common_tasks=("US_AuditDataPoints" "CA_AuditDataPoints" "US_DDBv2FileGeneration")
    for task in "${common_tasks[@]}"; do
        if aws logs describe-log-groups \
            --log-group-name-prefix "${task}" \
            --region "${REGION}" &> /dev/null; then
            log_success "Task log group exists: ${task}"
        else
            log_warning "Task log group not found: ${task} (may not be deployed yet)"
        fi
    done
}

# Main validation function
main() {
    log_info "Starting Processing Apps Migration Validation"
    log_info "Environment: ${ENVIRONMENT}"
    log_info "Region: ${REGION}"
    log_info "Cluster: ${CLUSTER_NAME}"
    echo
    
    local validation_errors=0
    
    # Run validations
    validate_prerequisites || ((validation_errors++))
    echo
    
    validate_ecs_cluster || ((validation_errors++))
    echo
    
    validate_autoscaling_group || ((validation_errors++))
    echo
    
    validate_lambda_function || ((validation_errors++))
    echo
    
    validate_cloudwatch_alarms || ((validation_errors++))
    echo
    
    validate_security_groups || ((validation_errors++))
    echo
    
    validate_log_groups || ((validation_errors++))
    echo
    
    # Summary
    if [[ ${validation_errors} -eq 0 ]]; then
        log_success "All validations passed! Migration appears successful."
        exit 0
    else
        log_error "Validation completed with ${validation_errors} error(s). Please review and fix issues."
        exit 1
    fi
}

# Script usage
usage() {
    echo "Usage: $0 [ENVIRONMENT] [REGION]"
    echo "  ENVIRONMENT: nonprod, prod, qamain, etc. (default: nonprod)"
    echo "  REGION: AWS region (default: us-east-1)"
    echo
    echo "Example: $0 nonprod us-east-1"
}

# Check for help flag
if [[ "${1:-}" == "-h" ]] || [[ "${1:-}" == "--help" ]]; then
    usage
    exit 0
fi

# Run main function
main "$@"
