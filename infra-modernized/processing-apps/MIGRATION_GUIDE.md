# Processing Apps Infrastructure Migration Guide

This guide provides step-by-step instructions for migrating from the legacy processing-apps infrastructure to the modernized version.

## Overview

The modernized infrastructure includes the following improvements:

- **Terraform 1.6+** with AWS Provider 5.x
- **Launch Templates** instead of Launch Configurations
- **Enhanced Security** with IMDSv2, encryption, and least privilege
- **Better Monitoring** with improved CloudWatch alarms and metrics
- **Modular Architecture** for better maintainability
- **Modern Terraform Syntax** using `templatefile()` instead of `data.template_file`

## Pre-Migration Checklist

### 1. Prerequisites
- [ ] Terraform >= 1.6.0 installed
- [ ] AWS CLI configured with appropriate permissions
- [ ] Access to existing Terraform state files
- [ ] Backup of current infrastructure state

### 2. Environment Preparation
- [ ] Review current infrastructure configuration
- [ ] Identify all dependent services and integrations
- [ ] Plan maintenance window for migration
- [ ] Notify stakeholders of planned changes

### 3. Validation
- [ ] Test modernized modules in a development environment
- [ ] Verify all container definitions are compatible
- [ ] Confirm Lambda packages are available
- [ ] Validate SNS topic configurations

## Migration Steps

### Step 1: Backup Current State

```bash
# Create backup directory
mkdir -p backups/$(date +%Y%m%d)

# Backup Terraform state
terraform state pull > backups/$(date +%Y%m%d)/terraform.tfstate.backup

# Export current resource configurations
terraform show -json > backups/$(date +%Y%m%d)/current-config.json
```

### Step 2: Prepare New Infrastructure

```bash
# Clone or copy the modernized infrastructure
cp -r infra-modernized/processing-apps /path/to/new/infrastructure

# Navigate to the appropriate environment
cd /path/to/new/infrastructure/environments/nonprod
```

### Step 3: Configure Variables

Create a `terraform.tfvars` file with your environment-specific values:

```hcl
# terraform.tfvars
region      = "us-east-1"
environment = "nonprod"

# Account Configuration
account_id   = "************"
account_type = "nonprod"

# Lambda Package Path
package_path_ecs_termination_protection = "/path/to/lambda/package.zip"

# Metadata
launched_by   = "migration-team"
launched_on   = "2024-01-15"
slack_contact = "#ais-infrastructure"
build_number  = "migration-v1.0"

# Override default values as needed
ecs_ami_ids = {
  "us-east-1" = "ami-0c02fb55956c7d316"
}
```

### Step 4: Initialize New Infrastructure

```bash
# Initialize Terraform with new providers
terraform init

# Validate configuration
terraform validate

# Plan the deployment
terraform plan -out=migration.tfplan
```

### Step 5: Review Migration Plan

Carefully review the Terraform plan to understand what will be:
- **Created**: New resources with modern configurations
- **Modified**: Existing resources that will be updated
- **Destroyed**: Legacy resources that will be replaced

**Important**: The following resources will be recreated:
- Launch Configuration → Launch Template
- Auto Scaling Group (due to launch template change)
- ECS Instances (will be replaced during ASG update)

### Step 6: Execute Migration

```bash
# Apply the migration plan
terraform apply migration.tfplan

# Monitor the deployment
watch -n 30 'aws ecs describe-clusters --clusters ais-nonprod-processing-apps-ecs'
```

### Step 7: Validate Migration

```bash
# Check ECS cluster status
aws ecs describe-clusters --clusters ais-nonprod-processing-apps-ecs

# Verify Auto Scaling Group
aws autoscaling describe-auto-scaling-groups --auto-scaling-group-names ais-nonprod-processing-apps-asg

# Check CloudWatch alarms
aws cloudwatch describe-alarms --alarm-name-prefix "ais-nonprod-processing-apps"

# Verify Lambda function
aws lambda get-function --function-name ais_nonprod_ecs_term_protection
```

## Post-Migration Tasks

### 1. Update Task Definitions

If you have existing task definitions, migrate them to use the new module:

```bash
# Navigate to task definitions
cd environments/nonprod

# Apply task definitions
terraform apply -target=module.us_audit_data_points
terraform apply -target=module.ca_audit_data_points
```

### 2. Verify Monitoring

- [ ] Check CloudWatch alarms are triggering correctly
- [ ] Verify SNS notifications are working
- [ ] Test auto-scaling policies
- [ ] Confirm log aggregation is functioning

### 3. Update Documentation

- [ ] Update runbooks with new resource names
- [ ] Update monitoring dashboards
- [ ] Update incident response procedures
- [ ] Document new module usage patterns

## Rollback Procedure

If issues are encountered during migration:

### 1. Immediate Rollback

```bash
# Restore from backup state
terraform state push backups/$(date +%Y%m%d)/terraform.tfstate.backup

# Apply previous configuration
terraform apply -auto-approve
```

### 2. Gradual Rollback

If partial migration was successful:

```bash
# Identify problematic resources
terraform state list

# Remove problematic resources from state
terraform state rm <resource_name>

# Re-import with previous configuration
terraform import <resource_type>.<resource_name> <resource_id>
```

## Troubleshooting

### Common Issues

#### 1. Launch Template Creation Fails
**Symptom**: Error creating launch template
**Solution**: Verify AMI ID is correct and accessible

#### 2. Auto Scaling Group Update Fails
**Symptom**: ASG fails to update with new launch template
**Solution**: Check instance limits and subnet capacity

#### 3. Lambda Function Deployment Fails
**Symptom**: Lambda package not found or invalid
**Solution**: Verify package path and permissions

#### 4. CloudWatch Alarms Not Triggering
**Symptom**: Alarms remain in INSUFFICIENT_DATA state
**Solution**: Check metric namespaces and log group names

### Getting Help

1. **Check Logs**: Review CloudWatch logs for detailed error messages
2. **Terraform Debug**: Run with `TF_LOG=DEBUG` for verbose output
3. **AWS Support**: Contact AWS support for service-specific issues
4. **Team Escalation**: Contact the infrastructure team for assistance

## Validation Checklist

After migration, verify the following:

### Infrastructure
- [ ] ECS cluster is running and healthy
- [ ] Auto Scaling Group has correct configuration
- [ ] Launch Template is using latest AMI
- [ ] Security Groups have proper rules
- [ ] Lambda function is deployed and functional

### Monitoring
- [ ] CloudWatch alarms are configured and active
- [ ] Log groups are created with correct retention
- [ ] SNS topics are receiving notifications
- [ ] Metric filters are capturing log events

### Functionality
- [ ] ECS tasks can be scheduled and run successfully
- [ ] Auto-scaling policies trigger correctly
- [ ] Termination protection works as expected
- [ ] Container definitions are compatible

### Security
- [ ] IAM roles have minimal required permissions
- [ ] Security groups follow least privilege principle
- [ ] Encryption is enabled where appropriate
- [ ] IMDSv2 is enforced on instances

## Success Criteria

The migration is considered successful when:

1. All infrastructure resources are deployed and healthy
2. Existing functionality is preserved
3. Monitoring and alerting are working correctly
4. No degradation in performance or reliability
5. All validation checks pass

## Next Steps

After successful migration:

1. **Monitor**: Keep close watch on the infrastructure for 48-72 hours
2. **Optimize**: Fine-tune auto-scaling policies and alarm thresholds
3. **Document**: Update all relevant documentation and procedures
4. **Train**: Ensure team members understand the new infrastructure
5. **Plan**: Schedule migration of other environments (QA, Production)

## Contact Information

For questions or issues during migration:

- **Infrastructure Team**: #ais-infrastructure
- **On-Call Engineer**: Use standard escalation procedures
- **Documentation**: See README.md for detailed module documentation
