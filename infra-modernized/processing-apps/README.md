# Processing Apps Infrastructure - Modernized

This directory contains the modernized Terraform infrastructure for the AIS 1.0 Processing Apps.

## Overview

The processing apps infrastructure provides ECS-based batch processing capabilities with auto-scaling, monitoring, and alerting. This modernized version includes:

- **Terraform 1.6+** compatibility
- **AWS Provider 5.x** with latest features
- **Modular architecture** for better maintainability
- **Improved security** and best practices
- **Enhanced monitoring** and observability

## Architecture

```
processing-apps/
├── modules/
│   ├── ecs-cluster/          # ECS cluster with auto-scaling
│   ├── task-definitions/     # ECS task definitions and scheduling
│   ├── monitoring/           # CloudWatch alarms and SNS alerts
│   └── security/             # Security groups and IAM roles
├── environments/
│   ├── nonprod/              # Non-production configurations
│   └── prod/                 # Production configurations
└── examples/                 # Usage examples
```

## Key Improvements

### 1. **Terraform Modernization**
- Upgraded from AWS provider 3.x to 5.x
- Replaced deprecated `data.template_file` with `templatefile()`
- Replaced `aws_launch_configuration` with `aws_launch_template`
- Added proper version constraints and variable validation

### 2. **Enhanced Security**
- Updated security group configurations
- Improved IAM role definitions
- Added encryption at rest and in transit
- Implemented least privilege access

### 3. **Better Monitoring**
- Enhanced CloudWatch alarms with proper thresholds
- Improved SNS topic configurations
- Added custom metrics and dashboards
- Better log aggregation and retention

### 4. **Code Quality**
- Consistent variable naming and validation
- Comprehensive documentation
- Proper module structure and organization
- Standardized tagging strategy

## Migration Guide

### Prerequisites
- Terraform >= 1.6.0
- AWS CLI configured with appropriate permissions
- Existing infrastructure state files

### Migration Steps

1. **Backup Current State**
   ```bash
   terraform state pull > backup-$(date +%Y%m%d).tfstate
   ```

2. **Update Provider Versions**
   - Review and update `versions.tf` files
   - Run `terraform init -upgrade`

3. **Plan Migration**
   ```bash
   terraform plan -out=migration.tfplan
   ```

4. **Apply Changes**
   ```bash
   terraform apply migration.tfplan
   ```

### Breaking Changes

- **Launch Configuration → Launch Template**: Resources will be recreated
- **Template File → Templatefile**: No infrastructure changes
- **Provider Upgrade**: Some resource attributes may change

## Usage

### Basic Example

```hcl
module "processing_apps" {
  source = "./modules/ecs-cluster"
  
  # Required variables
  application             = "ais"
  environment            = "nonprod"
  region                 = "us-east-1"
  
  # Networking
  vpc_id                 = data.aws_vpc.main.id
  private_subnet_ids     = data.aws_subnets.private.ids
  
  # Auto Scaling
  min_size               = 3
  max_size               = 18
  desired_capacity       = 3
  
  # Instance Configuration
  instance_type          = "m5.4xlarge"
  ami_id                 = data.aws_ami.ecs_optimized.id
  
  # Tags
  tags = local.common_tags
}
```

## Support

For questions or issues with the modernized infrastructure:

1. Review the migration guide above
2. Check the examples directory for usage patterns
3. Consult the original infrastructure documentation
4. Contact the infrastructure team

## Validation

The modernized infrastructure has been validated to:
- ✅ Maintain all existing functionality
- ✅ Preserve resource names and tags
- ✅ Support all current environments
- ✅ Provide equivalent monitoring and alerting
- ✅ Maintain backward compatibility where possible
