# =============================================================================
# LOCAL VALUES
# =============================================================================

locals {
  # Common naming convention
  name_prefix = "${var.application}-${var.environment}-${var.component}"
  
  # Common tags applied to all resources
  common_tags = merge(var.tags, {
    Application  = var.application
    Environment  = var.environment
    Service      = var.service
    Component    = var.component
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
  })
  
  # Task definition family name
  task_family = "${var.task_friendly_name}_${var.environment}"
  
  # Log group name
  log_group_name = var.task_friendly_name
}

# =============================================================================
# CLOUDWATCH LOG GROUP
# =============================================================================

resource "aws_cloudwatch_log_group" "task_logs" {
  name              = local.log_group_name
  retention_in_days = var.retention_in_days
  kms_key_id       = null # Use default encryption

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-${var.task_friendly_name}-logs"
  })
}

# =============================================================================
# ECS TASK DEFINITION
# =============================================================================

resource "aws_ecs_task_definition" "main" {
  family                   = local.task_family
  requires_compatibilities = var.requires_compatibilities
  task_role_arn           = var.task_role_arn
  execution_role_arn      = var.execution_role_arn
  network_mode            = "bridge"
  
  # Use templatefile instead of deprecated data.template_file
  container_definitions = templatefile(var.container_definition_path, {
    environment        = var.environment
    image_url_name_tag = var.image_url_name_tag
    ini_bucket         = var.ini_bucket
    country_iso_code   = var.country_iso_code
    task_friendly_name = var.task_friendly_name
    region             = var.region
    rds_backup_bucket  = var.rds_backup_bucket
    rrri_topic_arn     = var.rrri_topic_arn
    cpu                = var.cpu
    memory             = var.memory
    memory_reservation = var.memory_reservation
    log_group_name     = local.log_group_name
  })

  # EFS volume for shared data
  volume {
    name      = "aisdata"
    host_path = "/aisdata"
  }

  # Runtime platform for better compatibility
  runtime_platform {
    operating_system_family = "LINUX"
    cpu_architecture        = "X86_64"
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-${var.task_friendly_name}"
  })

  depends_on = [aws_cloudwatch_log_group.task_logs]
}

# =============================================================================
# SCHEDULED EVENT RULE (CONDITIONAL)
# =============================================================================

resource "aws_cloudwatch_event_rule" "scheduled_task" {
  count = var.schedule_expression != "" ? 1 : 0

  name                = "${var.task_friendly_name}_${var.environment}_schedule"
  description         = "Scheduled event for the ${var.task_friendly_name}_${var.environment} processing app"
  schedule_expression = "cron(${var.schedule_expression})"
  is_enabled         = var.enabled

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-${var.task_friendly_name}-schedule"
  })

  lifecycle {
    ignore_changes = [is_enabled]
  }
}

# =============================================================================
# SCHEDULED EVENT TARGET (CONDITIONAL)
# =============================================================================

resource "aws_cloudwatch_event_target" "ecs_scheduled_task" {
  count = var.schedule_expression != "" ? 1 : 0

  rule     = aws_cloudwatch_event_rule.scheduled_task[0].name
  arn      = data.aws_ecs_cluster.main.arn
  role_arn = var.execution_role_arn

  ecs_target {
    task_count          = var.task_count
    task_definition_arn = aws_ecs_task_definition.main.arn
    launch_type         = "EC2"
    
    # Network configuration for better reliability
    network_configuration {
      assign_public_ip = false
    }
    
    # Placement constraints to ensure tasks run on appropriate instances
    placement_constraint {
      type = "memberOf"
      expression = "attribute:ecs.instance-type =~ t3.*|m5.*|c5.*"
    }
  }

  # Retry configuration for failed tasks
  retry_policy {
    maximum_retry_attempts       = 3
    maximum_event_age_in_seconds = 3600
  }

  # Dead letter queue for failed events
  dead_letter_config {
    arn = aws_sqs_queue.task_dlq[0].arn
  }
}

# =============================================================================
# DEAD LETTER QUEUE FOR SCHEDULED TASKS (CONDITIONAL)
# =============================================================================

resource "aws_sqs_queue" "task_dlq" {
  count = var.schedule_expression != "" ? 1 : 0

  name                      = "${local.name_prefix}-${var.task_friendly_name}-dlq"
  message_retention_seconds = 1209600 # 14 days
  
  # Enable server-side encryption
  kms_master_key_id = "alias/aws/sqs"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-${var.task_friendly_name}-dlq"
  })
}

# =============================================================================
# DATA SOURCES
# =============================================================================

data "aws_ecs_cluster" "main" {
  cluster_name = "${var.application}-${var.environment}-${var.component}-ecs"
}
