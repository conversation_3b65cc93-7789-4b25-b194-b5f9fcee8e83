# =============================================================================
# REQUIRED VARIABLES
# =============================================================================

variable "application" {
  description = "The name of the application"
  type        = string
  
  validation {
    condition     = length(var.application) > 0
    error_message = "Application name cannot be empty."
  }
}

variable "service" {
  description = "The service name"
  type        = string
  
  validation {
    condition     = length(var.service) > 0
    error_message = "Service name cannot be empty."
  }
}

variable "region" {
  description = "The AWS region"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z]{2}-[a-z]+-[0-9]$", var.region))
    error_message = "Region must be a valid AWS region format (e.g., us-east-1)."
  }
}

variable "environment" {
  description = "The deployment environment"
  type        = string
  
  validation {
    condition     = contains(["nonprod", "prod", "qamain", "qarapid", "uat", "scratch", "homenet"], var.environment)
    error_message = "Environment must be one of: nonprod, prod, qamain, qarapid, uat, scratch, homenet."
  }
}

variable "component" {
  description = "The component name"
  type        = string
  
  validation {
    condition     = length(var.component) > 0
    error_message = "Component name cannot be empty."
  }
}

variable "country_iso_code" {
  description = "The country ISO code (US or CA)"
  type        = string
  
  validation {
    condition     = contains(["US", "CA"], var.country_iso_code)
    error_message = "Country ISO code must be either 'US' or 'CA'."
  }
}

# =============================================================================
# TASK DEFINITION VARIABLES
# =============================================================================

variable "task_friendly_name" {
  description = "The friendly name of the task definition"
  type        = string
  
  validation {
    condition     = length(var.task_friendly_name) > 0
    error_message = "Task friendly name cannot be empty."
  }
}

variable "container_definition_path" {
  description = "Path to the container definition JSON file"
  type        = string
  
  validation {
    condition     = length(var.container_definition_path) > 0
    error_message = "Container definition path cannot be empty."
  }
}

variable "task_role_arn" {
  description = "The ARN of IAM role that allows ECS container tasks to make calls to other AWS services"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:iam::[0-9]{12}:role/.+", var.task_role_arn))
    error_message = "Task role ARN must be a valid IAM role ARN."
  }
}

variable "execution_role_arn" {
  description = "The ARN of the task execution role that the ECS container agent and Docker daemon can assume"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:iam::[0-9]{12}:role/.+", var.execution_role_arn))
    error_message = "Execution role ARN must be a valid IAM role ARN."
  }
}

variable "requires_compatibilities" {
  description = "A set of launch types required by the task"
  type        = list(string)
  default     = ["EC2"]
  
  validation {
    condition = alltrue([
      for compat in var.requires_compatibilities : contains(["EC2", "FARGATE"], compat)
    ])
    error_message = "Requires compatibilities must be either 'EC2' or 'FARGATE'."
  }
}

variable "image_url_name_tag" {
  description = "The ECR URL and image name:tag that should be used for the container"
  type        = string
  
  validation {
    condition     = length(var.image_url_name_tag) > 0
    error_message = "Image URL name tag cannot be empty."
  }
}

variable "ini_bucket" {
  description = "The S3 bucket name for INI files"
  type        = string
  
  validation {
    condition     = length(var.ini_bucket) > 0
    error_message = "INI bucket name cannot be empty."
  }
}

variable "rds_backup_bucket" {
  description = "The S3 bucket name for RDS backups"
  type        = string
  default     = ""
}

variable "rrri_topic_arn" {
  description = "The ARN of the RRRI SNS topic"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:sns:[a-z0-9-]+:[0-9]{12}:.+", var.rrri_topic_arn))
    error_message = "RRRI topic ARN must be a valid SNS topic ARN."
  }
}

# =============================================================================
# CONTAINER CONFIGURATION VARIABLES
# =============================================================================

variable "cpu" {
  description = "The number of CPU units reserved for the container"
  type        = number
  default     = 512
  
  validation {
    condition     = var.cpu > 0
    error_message = "CPU must be greater than 0."
  }
}

variable "memory" {
  description = "The amount of memory (in MiB) reserved for the container"
  type        = number
  default     = 512
  
  validation {
    condition     = var.memory > 0
    error_message = "Memory must be greater than 0."
  }
}

variable "memory_reservation" {
  description = "The soft limit (in MiB) of memory to reserve for the container"
  type        = number
  default     = null
}

# =============================================================================
# SCHEDULING VARIABLES
# =============================================================================

variable "schedule_expression" {
  description = "The scheduling expression for the task (cron format)"
  type        = string
  default     = ""
}

variable "enabled" {
  description = "Whether the scheduled task is enabled"
  type        = bool
  default     = true
}

variable "task_count" {
  description = "The number of tasks to run"
  type        = number
  default     = 1
  
  validation {
    condition     = var.task_count > 0
    error_message = "Task count must be greater than 0."
  }
}

# =============================================================================
# LOGGING VARIABLES
# =============================================================================

variable "retention_in_days" {
  description = "CloudWatch log retention in days"
  type        = number
  default     = 7
  
  validation {
    condition = contains([
      1, 3, 5, 7, 14, 30, 60, 90, 120, 150, 180, 365, 400, 545, 731, 1827, 3653
    ], var.retention_in_days)
    error_message = "Retention in days must be a valid CloudWatch log retention value."
  }
}

# =============================================================================
# METADATA VARIABLES
# =============================================================================

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this infrastructure was launched"
  type        = string
  default     = ""
}

variable "slack_contact" {
  description = "Slack contact for this infrastructure"
  type        = string
  default     = ""
}

variable "build_number" {
  description = "The build number for this deployment"
  type        = string
  default     = "latest"
}

# =============================================================================
# TAGS VARIABLE
# =============================================================================

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
