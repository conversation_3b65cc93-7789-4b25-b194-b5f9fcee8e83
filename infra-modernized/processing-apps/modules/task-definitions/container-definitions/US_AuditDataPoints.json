[
    {
        "name": "${task_friendly_name}",
        "image": "${image_url_name_tag}",
        "cpu": ${cpu},
        "memory": ${memory},
        %{ if memory_reservation != null }
        "memoryReservation": ${memory_reservation},
        %{ endif }
        "essential": true,
        "environment": [
            {
                "name": "INI_BUCKET",
                "value": "${ini_bucket}"
            },
            {
                "name": "ENVIRONMENT",
                "value": "${environment}"
            },
            {
                "name": "COUNTRY",
                "value": "${country_iso_code}"
            },
            {
                "name": "INI_S3_FILENAME",
                "value": "iniPackage.zip"
            },
            {
                "name": "TASKNAME",
                "value": "${task_friendly_name}"
            },
            {
                "name": "AWS_DEFAULT_REGION",
                "value": "${region}"
            }
        ],
        "mountPoints": [
            {
                "sourceVolume": "aisdata",
                "containerPath": "/aisdata",
                "readOnly": false
            }
        ],
        "command": [
            "sudo -u \\#48 -g \\#48 INI_BUCKET=${ini_bucket} ENVIRONMENT=${environment} COUNTRY=${country_iso_code} INI_S3_FILENAME=iniPackage.zip TASKNAME=${task_friendly_name} sh /data/git/AIS-1.0/ShellScripts/AuditDataPoints.sh"
        ],
        "logConfiguration": {
            "logDriver": "awslogs",
            "options": {
                "awslogs-group": "${log_group_name}",
                "awslogs-region": "${region}",
                "awslogs-stream-prefix": "ecstasks",
                "awslogs-create-group": "true"
            }
        },
        "healthCheck": {
            "command": [
                "CMD-SHELL",
                "ps aux | grep -v grep | grep -q AuditDataPoints || exit 1"
            ],
            "interval": 30,
            "timeout": 5,
            "retries": 3,
            "startPeriod": 60
        },
        "stopTimeout": 120,
        "startTimeout": 300,
        "ulimits": [
            {
                "name": "nofile",
                "softLimit": 65536,
                "hardLimit": 65536
            }
        ],
        "dockerLabels": {
            "task": "${task_friendly_name}",
            "environment": "${environment}",
            "country": "${country_iso_code}",
            "version": "modernized"
        }
    }
]
