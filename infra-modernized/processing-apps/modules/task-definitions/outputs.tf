# =============================================================================
# TASK DEFINITION OUTPUTS
# =============================================================================

output "task_definition_arn" {
  description = "The ARN of the ECS task definition"
  value       = aws_ecs_task_definition.main.arn
}

output "task_definition_family" {
  description = "The family of the ECS task definition"
  value       = aws_ecs_task_definition.main.family
}

output "task_definition_revision" {
  description = "The revision of the ECS task definition"
  value       = aws_ecs_task_definition.main.revision
}

# =============================================================================
# LOG GROUP OUTPUTS
# =============================================================================

output "log_group_name" {
  description = "The name of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.task_logs.name
}

output "log_group_arn" {
  description = "The ARN of the CloudWatch log group"
  value       = aws_cloudwatch_log_group.task_logs.arn
}

# =============================================================================
# SCHEDULED EVENT OUTPUTS (CONDITIONAL)
# =============================================================================

output "scheduled_event_rule_arn" {
  description = "The ARN of the scheduled event rule (if scheduled)"
  value       = var.schedule_expression != "" ? aws_cloudwatch_event_rule.scheduled_task[0].arn : null
}

output "scheduled_event_rule_name" {
  description = "The name of the scheduled event rule (if scheduled)"
  value       = var.schedule_expression != "" ? aws_cloudwatch_event_rule.scheduled_task[0].name : null
}

# =============================================================================
# DEAD LETTER QUEUE OUTPUTS (CONDITIONAL)
# =============================================================================

output "dlq_arn" {
  description = "The ARN of the dead letter queue (if scheduled)"
  value       = var.schedule_expression != "" ? aws_sqs_queue.task_dlq[0].arn : null
}

output "dlq_url" {
  description = "The URL of the dead letter queue (if scheduled)"
  value       = var.schedule_expression != "" ? aws_sqs_queue.task_dlq[0].url : null
}

# =============================================================================
# TASK CONFIGURATION OUTPUTS
# =============================================================================

output "task_friendly_name" {
  description = "The friendly name of the task"
  value       = var.task_friendly_name
}

output "country_iso_code" {
  description = "The country ISO code for the task"
  value       = var.country_iso_code
}

output "is_scheduled" {
  description = "Whether this task is scheduled"
  value       = var.schedule_expression != ""
}

output "schedule_expression" {
  description = "The schedule expression for the task"
  value       = var.schedule_expression
}
