# =============================================================================
# LOCAL VALUES
# =============================================================================

locals {
  # Common naming convention
  name_prefix = "${var.application}-${var.environment}-${var.component}"
  
  # Common tags applied to all resources
  common_tags = merge(var.tags, {
    Application     = var.application
    Environment     = var.environment
    Service         = var.service
    Component       = var.component
    Release         = var.build_number
    LaunchedBy      = var.launched_by
    LaunchedOn      = var.launched_on
    SlackContact    = var.slack_contact
    "coxauto:ci-id" = var.component_id
  })
  
  # Instance name for tagging
  instance_name = "ecs-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
  
  # Security group name
  security_group_name = "sg-ec2-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
  
  # Lambda function name
  lambda_name = "lm-${var.region_abbreviated}-${var.application_abbreviated}-${var.service}-${var.environment}-${var.build_number}"
}

# =============================================================================
# ECS CLUSTER
# =============================================================================

resource "aws_ecs_cluster" "main" {
  name = "${local.name_prefix}-ecs"

  setting {
    name  = "containerInsights"
    value = "enabled"
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-ecs"
  })
}

# =============================================================================
# SECURITY GROUP
# =============================================================================

resource "aws_security_group" "ecs_instances" {
  name_prefix = "${local.name_prefix}-ec2-"
  description = "Security group for ECS instances in ${var.environment} environment"
  vpc_id      = var.vpc_id

  # Allow all outbound traffic
  egress {
    description = "All outbound traffic"
    from_port   = 0
    to_port     = 0
    protocol    = "-1"
    cidr_blocks = ["0.0.0.0/0"]
  }

  # Allow SSH from authorized networks
  ingress {
    description = "SSH access from authorized networks"
    from_port   = 22
    to_port     = 22
    protocol    = "tcp"
    cidr_blocks = [var.homenet_cidr, var.remote_cidr, var.ais_cidr]
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = local.security_group_name
  })
}

# =============================================================================
# LAUNCH TEMPLATE (MODERNIZED FROM LAUNCH CONFIGURATION)
# =============================================================================

resource "aws_launch_template" "ecs_instances" {
  name_prefix   = "${local.name_prefix}-"
  description   = "Launch template for ECS instances in ${var.environment}"
  image_id      = var.ami_id
  instance_type = var.instance_type
  key_name      = var.key_name

  vpc_security_group_ids = [var.security_group, aws_security_group.ecs_instances.id]

  iam_instance_profile {
    name = "ais10-ec2-for-ec2ecs-role"
  }

  user_data = base64encode(templatefile("${path.module}/userdata.tpl", {
    ecs_cluster_name = aws_ecs_cluster.main.name
    ecs_logging      = var.ecs_logging
    ec2_environment  = var.environment
    region           = var.region
    efs_id           = var.efs_id
  }))

  # Enable detailed monitoring
  monitoring {
    enabled = true
  }

  # EBS optimization for better performance
  ebs_optimized = true

  # Instance metadata service configuration (IMDSv2)
  metadata_options {
    http_endpoint               = "enabled"
    http_tokens                 = "required"
    http_put_response_hop_limit = 2
    instance_metadata_tags      = "enabled"
  }

  tag_specifications {
    resource_type = "instance"
    tags = merge(local.common_tags, {
      Name                    = local.instance_name
      Cluster                 = aws_ecs_cluster.main.name
      AppStackTypeForSSM      = "ProcessingApps"
      "ssm-patch-installation" = "true"
    })
  }

  tag_specifications {
    resource_type = "volume"
    tags = merge(local.common_tags, {
      Name = "${local.instance_name}-volume"
    })
  }

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-launch-template"
  })
}

# =============================================================================
# AUTO SCALING GROUP
# =============================================================================

resource "aws_autoscaling_group" "main" {
  name                = "${local.name_prefix}-asg"
  vpc_zone_identifier = var.private_subnet_ids
  target_group_arns   = []
  health_check_type   = "EC2"
  health_check_grace_period = 1000

  min_size         = var.min_size
  max_size         = var.max_size
  desired_capacity = var.desired_capacity

  wait_for_capacity_timeout = 0
  termination_policies      = ["ClosestToNextInstanceHour", "Default"]
  
  enabled_metrics = [
    "GroupMinSize",
    "GroupDesiredCapacity", 
    "GroupInServiceInstances",
    "GroupPendingInstances",
    "GroupTerminatingInstances",
    "GroupTotalInstances"
  ]

  launch_template {
    id      = aws_launch_template.ecs_instances.id
    version = "$Latest"
  }

  # Instance refresh configuration for rolling updates
  instance_refresh {
    strategy = "Rolling"
    preferences {
      min_healthy_percentage = 50
      instance_warmup        = 300
    }
  }

  lifecycle {
    create_before_destroy = true
    ignore_changes        = [desired_capacity]
  }

  # Use dynamic tags for better maintainability
  dynamic "tag" {
    for_each = merge(local.common_tags, {
      Name = "${local.name_prefix}-asg"
    })
    
    content {
      key                 = tag.key
      value               = tag.value
      propagate_at_launch = true
    }
  }
}

# =============================================================================
# AUTO SCALING LIFECYCLE HOOK
# =============================================================================

resource "aws_autoscaling_lifecycle_hook" "terminate_hook" {
  name                 = "${local.name_prefix}-ecs-termination-protection-lifecyclehook-terminate"
  autoscaling_group_name = aws_autoscaling_group.main.name
  default_result         = "CONTINUE"
  heartbeat_timeout      = 900
  lifecycle_transition   = "autoscaling:EC2_INSTANCE_TERMINATING"

  notification_metadata = jsonencode({
    cluster_name = aws_ecs_cluster.main.name
    environment  = var.environment
  })
}
