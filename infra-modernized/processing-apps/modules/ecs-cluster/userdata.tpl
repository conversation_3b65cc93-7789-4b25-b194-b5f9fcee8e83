#!/bin/bash

# =============================================================================
# ECS INSTANCE INITIALIZATION SCRIPT
# =============================================================================

set -euo pipefail

# Logging function
log() {
    echo "[$(date '+%Y-%m-%d %H:%M:%S')] $*" | tee -a /var/log/user-data.log
}

log "Starting ECS instance initialization"

# =============================================================================
# SYSTEM UPDATES AND PACKAGE INSTALLATION
# =============================================================================

log "Updating system packages"
yum update -y

log "Installing required packages"
yum install -y \
    nfs-utils \
    wget \
    yum-cron \
    chrony \
    python3-pip \
    awscli \
    amazon-cloudwatch-agent

# =============================================================================
# CONFIGURE AUTOMATIC SECURITY UPDATES
# =============================================================================

log "Configuring automatic security updates"
sed -i 's/^update_cmd.*=.*$/update_cmd = security/' /etc/yum/yum-cron.conf
sed -i 's/^update_messages.*=.*$/update_messages = yes/' /etc/yum/yum-cron.conf
sed -i 's/^download_updates.*=.*$/download_updates = yes/' /etc/yum/yum-cron.conf
sed -i 's/^apply_updates.*=.*$/apply_updates = yes/' /etc/yum/yum-cron.conf

systemctl enable yum-cron
systemctl start yum-cron

# =============================================================================
# CONFIGURE TIME SYNCHRONIZATION
# =============================================================================

log "Configuring time synchronization with Chrony"

# Remove NTP if present
yum remove -y 'ntp*' || true

# Configure Chrony for AWS time sync
cat > /etc/chrony.conf << 'EOF'
# AWS Time Sync Service
server 169.254.169.123 prefer iburst minpoll 4 maxpoll 4

# Record the rate at which the system clock gains/losses time
driftfile /var/lib/chrony/drift

# Allow the system clock to be stepped in the first three updates
makestep 1.0 3

# Enable kernel synchronization of the real-time clock (RTC)
rtcsync

# Enable hardware timestamping on all interfaces that support it
#hwtimestamp *

# Increase the minimum number of selectable sources required to adjust
# the system clock
#minsources 2

# Allow NTP client access from local network
#allow 192.168.0.0/16

# Serve time even if not synchronized to a time source
#local stratum 10

# Specify file containing keys for NTP authentication
#keyfile /etc/chrony.keys

# Get TAI-UTC offset and leap seconds from the system tz database
leapsectz right/UTC

# Specify directory for log files
logdir /var/log/chrony

# Select which information is logged
#log measurements statistics tracking
EOF

systemctl enable chronyd
systemctl start chronyd

# =============================================================================
# INSTALL AND CONFIGURE SSM AGENT
# =============================================================================

log "Installing SSM Agent"
yum install -y amazon-ssm-agent
systemctl enable amazon-ssm-agent
systemctl start amazon-ssm-agent

# =============================================================================
# CONFIGURE CLOUDWATCH MONITORING
# =============================================================================

log "Setting up CloudWatch monitoring"

# Install CloudWatch monitoring scripts
pip3 install boto3

# Create CloudWatch monitoring script
cat > /usr/local/bin/cloudwatch-metrics.py << 'EOF'
#!/usr/bin/env python3
import boto3
import psutil
import time
from datetime import datetime

def send_metrics():
    cloudwatch = boto3.client('cloudwatch', region_name='${region}')
    
    # Get system metrics
    cpu_percent = psutil.cpu_percent(interval=1)
    memory = psutil.virtual_memory()
    disk = psutil.disk_usage('/')
    
    # Prepare metrics
    metrics = [
        {
            'MetricName': 'MemoryUtilization',
            'Value': memory.percent,
            'Unit': 'Percent',
            'Timestamp': datetime.utcnow()
        },
        {
            'MetricName': 'DiskSpaceUtilization',
            'Value': (disk.used / disk.total) * 100,
            'Unit': 'Percent',
            'Timestamp': datetime.utcnow()
        }
    ]
    
    # Send metrics to CloudWatch
    try:
        cloudwatch.put_metric_data(
            Namespace='CWAgent',
            MetricData=metrics
        )
    except Exception as e:
        print(f"Error sending metrics: {e}")

if __name__ == "__main__":
    send_metrics()
EOF

chmod +x /usr/local/bin/cloudwatch-metrics.py

# Add cron job for metrics collection
echo "* * * * * /usr/local/bin/cloudwatch-metrics.py" | crontab -

# =============================================================================
# CONFIGURE EFS MOUNT
# =============================================================================

log "Configuring EFS mount for shared storage"

# Create mount point
mkdir -p /aisdata
chown 48:48 /aisdata

# Add EFS mount to fstab with optimized settings
echo "${efs_id}.efs.${region}.amazonaws.com:/ /aisdata nfs4 nfsvers=4.1,rsize=1048576,wsize=1048576,hard,intr,timeo=600,retrans=2 0 0" >> /etc/fstab

# Mount EFS
mount -a -t nfs4

# Verify mount
if mountpoint -q /aisdata; then
    log "EFS mounted successfully"
else
    log "ERROR: Failed to mount EFS"
    exit 1
fi

# =============================================================================
# CONFIGURE ECS AGENT
# =============================================================================

log "Configuring ECS agent"

# Create ECS configuration
cat > /etc/ecs/ecs.config << EOF
ECS_CLUSTER=${ecs_cluster_name}
ECS_AVAILABLE_LOGGING_DRIVERS=${ecs_logging}
ECS_ENABLE_TASK_IAM_ROLE=true
ECS_ENABLE_CONTAINER_METADATA=true
ECS_ENGINE_TASK_CLEANUP_WAIT_DURATION=10m
ECS_CONTAINER_STOP_TIMEOUT=30s
ECS_ENABLE_SPOT_INSTANCE_DRAINING=true
ECS_ENABLE_GPU_SUPPORT=false
ECS_DISABLE_PRIVILEGED=false
ECS_SELINUX_CAPABLE=false
ECS_APPARMOR_CAPABLE=false
ECS_ENABLE_TASK_ENI=false
ECS_DISABLE_DOCKER_HEALTH_CHECK=false
EOF

# Restart ECS agent to pick up new configuration
systemctl restart ecs

# =============================================================================
# CONFIGURE DOCKER
# =============================================================================

log "Configuring Docker daemon"

# Create Docker daemon configuration for better logging and performance
cat > /etc/docker/daemon.json << 'EOF'
{
    "log-driver": "awslogs",
    "log-opts": {
        "awslogs-region": "${region}",
        "awslogs-group": "ecs-agent"
    },
    "storage-driver": "overlay2",
    "max-concurrent-downloads": 10,
    "max-concurrent-uploads": 5,
    "default-ulimits": {
        "nofile": {
            "Name": "nofile",
            "Hard": 1024000,
            "Soft": 1024000
        }
    }
}
EOF

systemctl restart docker

# =============================================================================
# SECURITY HARDENING
# =============================================================================

log "Applying security hardening"

# Disable unused services
systemctl disable postfix || true
systemctl stop postfix || true

# Set secure permissions
chmod 600 /etc/ecs/ecs.config
chmod 600 /etc/docker/daemon.json

# Configure fail2ban for SSH protection (if available)
if command -v fail2ban-client &> /dev/null; then
    systemctl enable fail2ban
    systemctl start fail2ban
fi

# =============================================================================
# FINALIZATION
# =============================================================================

log "ECS instance initialization completed successfully"

# Signal that user data script has completed
/opt/aws/bin/cfn-signal -e $? --stack ${AWS::StackName} --resource AutoScalingGroup --region ${region} || true

log "Instance is ready for ECS tasks"
