# =============================================================================
# LAMBDA FUNCTION FOR ECS TERMINATION PROTECTION
# =============================================================================

resource "aws_lambda_function" "ecs_termination_protection" {
  filename         = var.package_path_ecs_termination_protection
  function_name    = "${var.application}_${var.environment}_ecs_term_protection"
  description      = "Processes lifecycle events for ${var.application} ${var.environment} EC2 instances, checks for running tasks and delays termination until tasks complete."
  role            = var.lambda_role_arn[var.account_type]
  handler         = "index.handler"
  runtime         = "nodejs18.x"
  source_code_hash = filebase64sha256(var.package_path_ecs_termination_protection)
  memory_size     = 128
  timeout         = 900

  environment {
    variables = {
      Environment  = var.environment
      ClusterName  = aws_ecs_cluster.main.name
      Region       = var.region
    }
  }

  # Enable X-Ray tracing for better observability
  tracing_config {
    mode = "Active"
  }

  # Dead letter queue for failed invocations
  dead_letter_config {
    target_arn = aws_sqs_queue.lambda_dlq.arn
  }

  tags = merge(local.common_tags, {
    Name            = local.lambda_name
    ApplicationPart = "ECSTerminationProtection"
  })

  depends_on = [aws_cloudwatch_log_group.lambda_logs]
}

# =============================================================================
# CLOUDWATCH LOG GROUP FOR LAMBDA
# =============================================================================

resource "aws_cloudwatch_log_group" "lambda_logs" {
  name              = "/aws/lambda/${var.application}_${var.environment}_ecs_term_protection"
  retention_in_days = 7
  kms_key_id       = null # Use default encryption

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-lambda-logs"
  })
}

# =============================================================================
# DEAD LETTER QUEUE FOR LAMBDA
# =============================================================================

resource "aws_sqs_queue" "lambda_dlq" {
  name                      = "${local.name_prefix}-lambda-dlq"
  message_retention_seconds = 1209600 # 14 days
  
  # Enable server-side encryption
  kms_master_key_id = "alias/aws/sqs"

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-lambda-dlq"
  })
}

# =============================================================================
# CLOUDWATCH EVENT RULE FOR AUTO SCALING LIFECYCLE
# =============================================================================

resource "aws_cloudwatch_event_rule" "autoscaling_lifecycle" {
  name        = "${local.name_prefix}-ecs-term-protection"
  description = "Capture autoscale lifecycle events for ${var.application} ${var.environment} and send them to Lambda"

  event_pattern = jsonencode({
    source      = ["aws.autoscaling"]
    detail-type = ["EC2 Instance-terminate Lifecycle Action"]
    detail = {
      AutoScalingGroupName = [aws_autoscaling_group.main.name]
    }
  })

  lifecycle {
    create_before_destroy = true
  }

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-ecs-term-protection-rule"
  })
}

# =============================================================================
# CLOUDWATCH EVENT TARGET
# =============================================================================

resource "aws_cloudwatch_event_target" "lambda_target" {
  rule      = aws_cloudwatch_event_rule.autoscaling_lifecycle.name
  target_id = "LambdaTarget"
  arn       = aws_lambda_function.ecs_termination_protection.arn

  # Add retry policy for failed invocations
  retry_policy {
    maximum_retry_attempts       = 3
    maximum_event_age_in_seconds = 3600
  }

  # Send failed events to dead letter queue
  dead_letter_config {
    arn = aws_sqs_queue.lambda_dlq.arn
  }
}

# =============================================================================
# LAMBDA PERMISSION FOR CLOUDWATCH EVENTS
# =============================================================================

resource "aws_lambda_permission" "allow_cloudwatch_events" {
  statement_id  = "${aws_cloudwatch_event_rule.autoscaling_lifecycle.name}_InvokePermission"
  action        = "lambda:InvokeFunction"
  function_name = aws_lambda_function.ecs_termination_protection.function_name
  principal     = "events.amazonaws.com"
  source_arn    = aws_cloudwatch_event_rule.autoscaling_lifecycle.arn
}
