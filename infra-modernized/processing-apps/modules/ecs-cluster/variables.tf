# =============================================================================
# REQUIRED VARIABLES
# =============================================================================

variable "application" {
  description = "The name of the application"
  type        = string
  
  validation {
    condition     = length(var.application) > 0
    error_message = "Application name cannot be empty."
  }
}

variable "application_abbreviated" {
  description = "The abbreviated name of the application"
  type        = string
  
  validation {
    condition     = length(var.application_abbreviated) > 0
    error_message = "Application abbreviated name cannot be empty."
  }
}

variable "service" {
  description = "The service name"
  type        = string
  
  validation {
    condition     = length(var.service) > 0
    error_message = "Service name cannot be empty."
  }
}

variable "region" {
  description = "The AWS region"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z]{2}-[a-z]+-[0-9]$", var.region))
    error_message = "Region must be a valid AWS region format (e.g., us-east-1)."
  }
}

variable "region_abbreviated" {
  description = "The abbreviated AWS region"
  type        = string
  
  validation {
    condition     = length(var.region_abbreviated) > 0
    error_message = "Region abbreviated cannot be empty."
  }
}

variable "environment" {
  description = "The deployment environment"
  type        = string
  
  validation {
    condition     = contains(["nonprod", "prod", "qamain", "qarapid", "uat", "scratch", "homenet"], var.environment)
    error_message = "Environment must be one of: nonprod, prod, qamain, qarapid, uat, scratch, homenet."
  }
}

variable "component" {
  description = "The component name"
  type        = string
  
  validation {
    condition     = length(var.component) > 0
    error_message = "Component name cannot be empty."
  }
}

variable "component_id" {
  description = "The component ID for tagging"
  type        = string
  
  validation {
    condition     = can(regex("^CI[0-9]+$", var.component_id))
    error_message = "Component ID must be in format CI followed by numbers (e.g., CI0934608)."
  }
}

variable "vpc_id" {
  description = "The VPC ID where resources will be created"
  type        = string
  
  validation {
    condition     = can(regex("^vpc-[a-z0-9]+$", var.vpc_id))
    error_message = "VPC ID must be a valid AWS VPC ID format."
  }
}

variable "private_subnet_ids" {
  description = "List of private subnet IDs for the Auto Scaling Group"
  type        = list(string)
  
  validation {
    condition     = length(var.private_subnet_ids) > 0
    error_message = "At least one private subnet ID must be provided."
  }
  
  validation {
    condition = alltrue([
      for subnet_id in var.private_subnet_ids : can(regex("^subnet-[a-z0-9]+$", subnet_id))
    ])
    error_message = "All subnet IDs must be valid AWS subnet ID format."
  }
}

variable "availability_zones" {
  description = "List of availability zones"
  type        = list(string)
  
  validation {
    condition     = length(var.availability_zones) > 0
    error_message = "At least one availability zone must be provided."
  }
}

variable "efs_id" {
  description = "The EFS file system ID for shared storage"
  type        = string
  
  validation {
    condition     = can(regex("^fs-[a-z0-9]+$", var.efs_id))
    error_message = "EFS ID must be a valid AWS EFS file system ID format."
  }
}

variable "security_group" {
  description = "The security group ID for EFS access"
  type        = string
  
  validation {
    condition     = can(regex("^sg-[a-z0-9]+$", var.security_group))
    error_message = "Security group must be a valid AWS security group ID format."
  }
}

variable "ami_id" {
  description = "The AMI ID for ECS instances"
  type        = string
  
  validation {
    condition     = can(regex("^ami-[a-z0-9]+$", var.ami_id))
    error_message = "AMI ID must be a valid AWS AMI ID format."
  }
}

variable "package_path_ecs_termination_protection" {
  description = "The path to the lambda code zip for ECS termination protection"
  type        = string
  
  validation {
    condition     = length(var.package_path_ecs_termination_protection) > 0
    error_message = "Lambda package path cannot be empty."
  }
}

variable "account_type" {
  description = "The account type (nonprod or prod)"
  type        = string
  
  validation {
    condition     = contains(["nonprod", "prod"], var.account_type)
    error_message = "Account type must be either 'nonprod' or 'prod'."
  }
}

# =============================================================================
# NETWORKING VARIABLES
# =============================================================================

variable "homenet_cidr" {
  description = "The CIDR block for homenet access"
  type        = string
  
  validation {
    condition     = can(cidrhost(var.homenet_cidr, 0))
    error_message = "Homenet CIDR must be a valid CIDR block."
  }
}

variable "ais_cidr" {
  description = "The CIDR block for AIS network access"
  type        = string
  
  validation {
    condition     = can(cidrhost(var.ais_cidr, 0))
    error_message = "AIS CIDR must be a valid CIDR block."
  }
}

variable "remote_cidr" {
  description = "The CIDR block for remote access"
  type        = string
  
  validation {
    condition     = can(cidrhost(var.remote_cidr, 0))
    error_message = "Remote CIDR must be a valid CIDR block."
  }
}

variable "nfs_cidr" {
  description = "The CIDR block for NFS access"
  type        = string
  
  validation {
    condition     = can(cidrhost(var.nfs_cidr, 0))
    error_message = "NFS CIDR must be a valid CIDR block."
  }
}

# =============================================================================
# METADATA VARIABLES
# =============================================================================

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this infrastructure was launched"
  type        = string
  default     = ""
}

variable "slack_contact" {
  description = "Slack contact for this infrastructure"
  type        = string
  default     = ""
}

variable "build_number" {
  description = "The build number for this deployment"
  type        = string
  default     = "latest"
}

# =============================================================================
# AUTO SCALING VARIABLES
# =============================================================================

variable "min_size" {
  description = "Minimum number of EC2 instances in the Auto Scaling Group"
  type        = number
  default     = 3

  validation {
    condition     = var.min_size >= 0
    error_message = "Minimum size must be greater than or equal to 0."
  }
}

variable "max_size" {
  description = "Maximum number of EC2 instances in the Auto Scaling Group"
  type        = number
  default     = 18

  validation {
    condition     = var.max_size >= var.min_size
    error_message = "Maximum size must be greater than or equal to minimum size."
  }
}

variable "desired_capacity" {
  description = "Desired number of EC2 instances in the Auto Scaling Group"
  type        = number
  default     = 3

  validation {
    condition     = var.desired_capacity >= var.min_size && var.desired_capacity <= var.max_size
    error_message = "Desired capacity must be between minimum and maximum size."
  }
}

variable "scale_out_adjustment" {
  description = "Number of instances to add when scaling out"
  type        = number
  default     = 4

  validation {
    condition     = var.scale_out_adjustment > 0
    error_message = "Scale out adjustment must be greater than 0."
  }
}

variable "scale_out_cooldown" {
  description = "Cooldown period in seconds after scaling out"
  type        = number
  default     = 60

  validation {
    condition     = var.scale_out_cooldown >= 0
    error_message = "Scale out cooldown must be greater than or equal to 0."
  }
}

variable "scale_in_adjustment" {
  description = "Number of instances to remove when scaling in (negative value)"
  type        = number
  default     = -1

  validation {
    condition     = var.scale_in_adjustment < 0
    error_message = "Scale in adjustment must be negative."
  }
}

variable "scale_in_cooldown" {
  description = "Cooldown period in seconds after scaling in"
  type        = number
  default     = 60

  validation {
    condition     = var.scale_in_cooldown >= 0
    error_message = "Scale in cooldown must be greater than or equal to 0."
  }
}

# =============================================================================
# INSTANCE CONFIGURATION VARIABLES
# =============================================================================

variable "instance_type" {
  description = "EC2 instance type for the ECS cluster"
  type        = string
  default     = "m5.large"

  validation {
    condition = can(regex("^[a-z][0-9][a-z]?\\.[a-z0-9]+$", var.instance_type))
    error_message = "Instance type must be a valid EC2 instance type format."
  }
}

variable "key_name" {
  description = "Name of the EC2 Key Pair for SSH access"
  type        = string
  default     = "AIS10"
}

variable "ecs_logging" {
  description = "ECS logging drivers configuration"
  type        = string
  default     = "[\"json-file\",\"awslogs\"]"
}

# =============================================================================
# SCHEDULING VARIABLES
# =============================================================================

variable "scheduling_enabled" {
  description = "Whether autoscaling scheduling is enabled"
  type        = bool
  default     = true
}

variable "scheduling_normal_map" {
  description = "Autoscaling scheduler map for normal business hours"
  type        = map(string)
  default = {
    "default.morning" = "0 12 * * MON-FRI"
    "default.night"   = "0 2 * * TUE-SAT"
  }
}

variable "extended_scheduling_enabled" {
  description = "Whether extended autoscaling scheduling is enabled"
  type        = bool
  default     = false
}

variable "scheduling_extended_map" {
  description = "Autoscaling scheduler map for extended business hours"
  type        = map(string)
  default = {
    "default.morning" = "0 10 1,2 * *"
    "default.night"   = "0 4 2,3 * *"
  }
}

# =============================================================================
# CLOUDWATCH ALARM VARIABLES
# =============================================================================

variable "low_cpu_threshold" {
  description = "CPU threshold for scale-in alarm"
  type        = number
  default     = 20

  validation {
    condition     = var.low_cpu_threshold >= 0 && var.low_cpu_threshold <= 100
    error_message = "CPU threshold must be between 0 and 100."
  }
}

variable "low_cpu_period" {
  description = "Period in seconds for low CPU alarm"
  type        = number
  default     = 300

  validation {
    condition     = var.low_cpu_period > 0
    error_message = "Period must be greater than 0."
  }
}

variable "low_cpu_evaluation_periods" {
  description = "Number of evaluation periods for low CPU alarm"
  type        = number
  default     = 40

  validation {
    condition     = var.low_cpu_evaluation_periods > 0
    error_message = "Evaluation periods must be greater than 0."
  }
}

variable "high_cpu_threshold" {
  description = "CPU threshold for scale-out alarm"
  type        = number
  default     = 40

  validation {
    condition     = var.high_cpu_threshold >= 0 && var.high_cpu_threshold <= 100
    error_message = "CPU threshold must be between 0 and 100."
  }
}

variable "high_cpu_failsafe_threshold" {
  description = "CPU threshold for failsafe scale-out alarm"
  type        = number
  default     = 55

  validation {
    condition     = var.high_cpu_failsafe_threshold >= 0 && var.high_cpu_failsafe_threshold <= 100
    error_message = "CPU threshold must be between 0 and 100."
  }
}

variable "high_cpu_period" {
  description = "Period in seconds for high CPU alarm"
  type        = number
  default     = 60

  validation {
    condition     = var.high_cpu_period > 0
    error_message = "Period must be greater than 0."
  }
}

variable "high_cpu_evaluation_periods" {
  description = "Number of evaluation periods for high CPU alarm"
  type        = number
  default     = 2

  validation {
    condition     = var.high_cpu_evaluation_periods > 0
    error_message = "Evaluation periods must be greater than 0."
  }
}

variable "high_cpu_datapoints_to_alarm" {
  description = "Number of datapoints that must be breaching to trigger alarm"
  type        = number
  default     = 2

  validation {
    condition     = var.high_cpu_datapoints_to_alarm > 0
    error_message = "Datapoints to alarm must be greater than 0."
  }
}

# =============================================================================
# MEMORY ALARM VARIABLES
# =============================================================================

variable "low_memory_threshold" {
  description = "Memory threshold for scale-in alarm"
  type        = number
  default     = 30

  validation {
    condition     = var.low_memory_threshold >= 0 && var.low_memory_threshold <= 100
    error_message = "Memory threshold must be between 0 and 100."
  }
}

variable "low_memory_period" {
  description = "Period in seconds for low memory alarm"
  type        = number
  default     = 300

  validation {
    condition     = var.low_memory_period > 0
    error_message = "Period must be greater than 0."
  }
}

variable "low_memory_evaluation_periods" {
  description = "Number of evaluation periods for low memory alarm"
  type        = number
  default     = 40

  validation {
    condition     = var.low_memory_evaluation_periods > 0
    error_message = "Evaluation periods must be greater than 0."
  }
}

variable "high_memory_threshold" {
  description = "Memory threshold for scale-out alarm"
  type        = number
  default     = 50

  validation {
    condition     = var.high_memory_threshold >= 0 && var.high_memory_threshold <= 100
    error_message = "Memory threshold must be between 0 and 100."
  }
}

variable "high_memory_failsafe_threshold" {
  description = "Memory threshold for failsafe scale-out alarm"
  type        = number
  default     = 60

  validation {
    condition     = var.high_memory_failsafe_threshold >= 0 && var.high_memory_failsafe_threshold <= 100
    error_message = "Memory threshold must be between 0 and 100."
  }
}

variable "high_memory_period" {
  description = "Period in seconds for high memory alarm"
  type        = number
  default     = 60

  validation {
    condition     = var.high_memory_period > 0
    error_message = "Period must be greater than 0."
  }
}

variable "high_memory_evaluation_periods" {
  description = "Number of evaluation periods for high memory alarm"
  type        = number
  default     = 2

  validation {
    condition     = var.high_memory_evaluation_periods > 0
    error_message = "Evaluation periods must be greater than 0."
  }
}

# =============================================================================
# LAMBDA CONFIGURATION VARIABLES
# =============================================================================

variable "lambda_role_arn" {
  description = "Map of Lambda execution role ARNs by account type"
  type        = map(string)
  default = {
    "nonprod" = "arn:aws:iam::************:role/acct-managed/ais10-ecs-termination-protection-lambda-role"
    "prod"    = "arn:aws:iam::************:role/acct-managed/ais10-ecs-termination-protection-lambda-role"
  }
}

# =============================================================================
# TAGS VARIABLE
# =============================================================================

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
