# =============================================================================
# CLOUDWATCH ALARMS FOR AUTO SCALING
# =============================================================================

# CPU-based alarms
resource "aws_cloudwatch_metric_alarm" "cpu_low" {
  alarm_name          = "${local.name_prefix}-CPU-Low"
  alarm_description   = "Triggered when CPU Reservation <= ${var.low_cpu_threshold}% for ${var.low_cpu_evaluation_periods} period(s) of ${var.low_cpu_period} seconds."
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = var.low_cpu_evaluation_periods
  metric_name         = "CPUReservation"
  namespace           = "AWS/ECS"
  period              = var.low_cpu_period
  statistic           = "Average"
  threshold           = var.low_cpu_threshold
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_in.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-CPU-Low"
  })
}

resource "aws_cloudwatch_metric_alarm" "cpu_high" {
  alarm_name          = "${local.name_prefix}-CPU-High"
  alarm_description   = "Triggered when CPU Reservation >= ${var.high_cpu_threshold}% for ${var.high_cpu_evaluation_periods} period(s) of ${var.high_cpu_period} seconds."
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.high_cpu_evaluation_periods
  metric_name         = "CPUReservation"
  namespace           = "AWS/ECS"
  period              = var.high_cpu_period
  statistic           = "Average"
  threshold           = var.high_cpu_threshold
  datapoints_to_alarm = var.high_cpu_datapoints_to_alarm
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-CPU-High"
  })
}

resource "aws_cloudwatch_metric_alarm" "cpu_high_failsafe" {
  alarm_name          = "${local.name_prefix}-CPU-High-Failsafe"
  alarm_description   = "Failsafe triggered when CPU Reservation >= ${var.high_cpu_failsafe_threshold}% for ${var.high_cpu_evaluation_periods} period(s) of ${var.high_cpu_period} seconds."
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.high_cpu_evaluation_periods
  metric_name         = "CPUReservation"
  namespace           = "AWS/ECS"
  period              = var.high_cpu_period
  statistic           = "Average"
  threshold           = var.high_cpu_failsafe_threshold
  datapoints_to_alarm = var.high_cpu_datapoints_to_alarm
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-CPU-High-Failsafe"
  })
}

# Memory-based alarms
resource "aws_cloudwatch_metric_alarm" "memory_low" {
  alarm_name          = "${local.name_prefix}-Memory-Low"
  alarm_description   = "Triggered when Memory Reservation <= ${var.low_memory_threshold}% for ${var.low_memory_evaluation_periods} period(s) of ${var.low_memory_period} seconds."
  comparison_operator = "LessThanOrEqualToThreshold"
  evaluation_periods  = var.low_memory_evaluation_periods
  metric_name         = "MemoryReservation"
  namespace           = "AWS/ECS"
  period              = var.low_memory_period
  statistic           = "Average"
  threshold           = var.low_memory_threshold
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_in.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-Memory-Low"
  })
}

resource "aws_cloudwatch_metric_alarm" "memory_high" {
  alarm_name          = "${local.name_prefix}-Memory-High"
  alarm_description   = "Triggered when Memory Reservation >= ${var.high_memory_threshold}% for ${var.high_memory_evaluation_periods} period(s) of ${var.high_memory_period} seconds."
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.high_memory_evaluation_periods
  metric_name         = "MemoryReservation"
  namespace           = "AWS/ECS"
  period              = var.high_memory_period
  statistic           = "Average"
  threshold           = var.high_memory_threshold
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-Memory-High"
  })
}

resource "aws_cloudwatch_metric_alarm" "memory_high_failsafe" {
  alarm_name          = "${local.name_prefix}-Memory-High-Failsafe"
  alarm_description   = "Failsafe triggered when Memory Reservation >= ${var.high_memory_failsafe_threshold}% for ${var.high_memory_evaluation_periods} period(s) of ${var.high_memory_period} seconds."
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = var.high_memory_evaluation_periods
  metric_name         = "MemoryReservation"
  namespace           = "AWS/ECS"
  period              = var.high_memory_period
  statistic           = "Average"
  threshold           = var.high_memory_failsafe_threshold
  treat_missing_data  = "notBreaching"

  dimensions = {
    ClusterName = aws_ecs_cluster.main.name
  }

  alarm_actions = [aws_autoscaling_policy.scale_out.arn]

  tags = merge(local.common_tags, {
    Name = "${local.name_prefix}-Memory-High-Failsafe"
  })
}
