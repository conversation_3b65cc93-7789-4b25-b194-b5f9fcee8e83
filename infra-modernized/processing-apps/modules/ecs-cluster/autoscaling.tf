# =============================================================================
# AUTO SCALING POLICIES
# =============================================================================

resource "aws_autoscaling_policy" "scale_out" {
  name                   = "${local.name_prefix}-scale-out"
  autoscaling_group_name = aws_autoscaling_group.main.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = var.scale_out_adjustment
  cooldown              = var.scale_out_cooldown
  policy_type           = "SimpleScaling"

  tags = local.common_tags
}

resource "aws_autoscaling_policy" "scale_in" {
  name                   = "${local.name_prefix}-scale-in"
  autoscaling_group_name = aws_autoscaling_group.main.name
  adjustment_type        = "ChangeInCapacity"
  scaling_adjustment     = var.scale_in_adjustment
  cooldown              = var.scale_in_cooldown
  policy_type           = "SimpleScaling"

  tags = local.common_tags
}

# =============================================================================
# SCHEDULED SCALING ACTIONS
# =============================================================================

resource "aws_autoscaling_schedule" "normal_morning_scale_up" {
  count = var.scheduling_enabled ? 1 : 0

  scheduled_action_name  = "normal-morning-scale-up-${md5(lookup(var.scheduling_normal_map, "${var.environment}.morning", var.scheduling_normal_map["default.morning"]))}"
  min_size              = var.min_size
  max_size              = var.max_size
  desired_capacity      = var.desired_capacity
  recurrence            = lookup(var.scheduling_normal_map, "${var.environment}.morning", var.scheduling_normal_map["default.morning"])
  autoscaling_group_name = aws_autoscaling_group.main.name
  time_zone             = "America/New_York"
}

resource "aws_autoscaling_schedule" "normal_evening_scale_down" {
  count = var.scheduling_enabled ? 1 : 0

  scheduled_action_name  = "normal-evening-scale-down-${md5(lookup(var.scheduling_normal_map, "${var.environment}.night", var.scheduling_normal_map["default.night"]))}"
  min_size              = 0
  max_size              = 0
  desired_capacity      = 0
  recurrence            = lookup(var.scheduling_normal_map, "${var.environment}.night", var.scheduling_normal_map["default.night"])
  autoscaling_group_name = aws_autoscaling_group.main.name
  time_zone             = "America/New_York"
}

resource "aws_autoscaling_schedule" "extended_morning_scale_up" {
  count = var.extended_scheduling_enabled ? 1 : 0

  scheduled_action_name  = "extended-morning-scale-up-${md5(lookup(var.scheduling_extended_map, "${var.environment}.morning", var.scheduling_extended_map["default.morning"]))}"
  min_size              = var.min_size
  max_size              = var.max_size
  desired_capacity      = var.desired_capacity
  recurrence            = lookup(var.scheduling_extended_map, "${var.environment}.morning", var.scheduling_extended_map["default.morning"])
  autoscaling_group_name = aws_autoscaling_group.main.name
  time_zone             = "America/New_York"
}

resource "aws_autoscaling_schedule" "extended_evening_scale_down" {
  count = var.extended_scheduling_enabled ? 1 : 0

  scheduled_action_name  = "extended-evening-scale-down-${md5(lookup(var.scheduling_extended_map, "${var.environment}.night", var.scheduling_extended_map["default.night"]))}"
  min_size              = 0
  max_size              = 0
  desired_capacity      = 0
  recurrence            = lookup(var.scheduling_extended_map, "${var.environment}.night", var.scheduling_extended_map["default.night"])
  autoscaling_group_name = aws_autoscaling_group.main.name
  time_zone             = "America/New_York"
}
