# =============================================================================
# ECS CLUSTER OUTPUTS
# =============================================================================

output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = aws_ecs_cluster.main.id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = aws_ecs_cluster.main.arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = aws_ecs_cluster.main.name
}

# =============================================================================
# AUTO SCALING GROUP OUTPUTS
# =============================================================================

output "autoscaling_group_id" {
  description = "The ID of the Auto Scaling Group"
  value       = aws_autoscaling_group.main.id
}

output "autoscaling_group_arn" {
  description = "The ARN of the Auto Scaling Group"
  value       = aws_autoscaling_group.main.arn
}

output "autoscaling_group_name" {
  description = "The name of the Auto Scaling Group"
  value       = aws_autoscaling_group.main.name
}

# =============================================================================
# LAUNCH TEMPLATE OUTPUTS
# =============================================================================

output "launch_template_id" {
  description = "The ID of the launch template"
  value       = aws_launch_template.ecs_instances.id
}

output "launch_template_arn" {
  description = "The ARN of the launch template"
  value       = aws_launch_template.ecs_instances.arn
}

output "launch_template_latest_version" {
  description = "The latest version of the launch template"
  value       = aws_launch_template.ecs_instances.latest_version
}

# =============================================================================
# SECURITY GROUP OUTPUTS
# =============================================================================

output "security_group_id" {
  description = "The ID of the ECS instances security group"
  value       = aws_security_group.ecs_instances.id
}

output "security_group_arn" {
  description = "The ARN of the ECS instances security group"
  value       = aws_security_group.ecs_instances.arn
}

# =============================================================================
# LAMBDA FUNCTION OUTPUTS
# =============================================================================

output "lambda_function_arn" {
  description = "The ARN of the ECS termination protection Lambda function"
  value       = aws_lambda_function.ecs_termination_protection.arn
}

output "lambda_function_name" {
  description = "The name of the ECS termination protection Lambda function"
  value       = aws_lambda_function.ecs_termination_protection.function_name
}

output "lambda_log_group_name" {
  description = "The name of the Lambda function's CloudWatch log group"
  value       = aws_cloudwatch_log_group.lambda_logs.name
}

# =============================================================================
# AUTO SCALING POLICY OUTPUTS
# =============================================================================

output "scale_out_policy_arn" {
  description = "The ARN of the scale out policy"
  value       = aws_autoscaling_policy.scale_out.arn
}

output "scale_in_policy_arn" {
  description = "The ARN of the scale in policy"
  value       = aws_autoscaling_policy.scale_in.arn
}

# =============================================================================
# CLOUDWATCH ALARM OUTPUTS
# =============================================================================

output "cpu_high_alarm_arn" {
  description = "The ARN of the high CPU alarm"
  value       = aws_cloudwatch_metric_alarm.cpu_high.arn
}

output "cpu_low_alarm_arn" {
  description = "The ARN of the low CPU alarm"
  value       = aws_cloudwatch_metric_alarm.cpu_low.arn
}

output "memory_high_alarm_arn" {
  description = "The ARN of the high memory alarm"
  value       = aws_cloudwatch_metric_alarm.memory_high.arn
}

output "memory_low_alarm_arn" {
  description = "The ARN of the low memory alarm"
  value       = aws_cloudwatch_metric_alarm.memory_low.arn
}

# =============================================================================
# EVENT RULE OUTPUTS
# =============================================================================

output "lifecycle_event_rule_arn" {
  description = "The ARN of the lifecycle event rule"
  value       = aws_cloudwatch_event_rule.autoscaling_lifecycle.arn
}

output "lifecycle_event_rule_name" {
  description = "The name of the lifecycle event rule"
  value       = aws_cloudwatch_event_rule.autoscaling_lifecycle.name
}

# =============================================================================
# DEAD LETTER QUEUE OUTPUTS
# =============================================================================

output "lambda_dlq_arn" {
  description = "The ARN of the Lambda dead letter queue"
  value       = aws_sqs_queue.lambda_dlq.arn
}

output "lambda_dlq_url" {
  description = "The URL of the Lambda dead letter queue"
  value       = aws_sqs_queue.lambda_dlq.url
}
