# =============================================================================
# DATABASE ERROR MONITORING
# =============================================================================

# =============================================================================
# DDBV1 US DATABASE ERROR MONITORING - CRITICAL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv1_critical_error_us" {
  name           = "DDBv1_Error_MetricFilter_US"
  pattern        = "?\"ERROR homenet\" ?\"DDB.SQL\""
  log_group_name = "US_DDBv1FileCreation_${var.environment}"

  metric_transformation {
    name      = "DDBv1_Error_Filter_US_${var.environment}"
    namespace = "DDBv1_Error_Filter_US_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv1_critical_error_us" {
  alarm_name          = "us_ddbv1_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv1_Error_Filter_US_${var.environment}"
  namespace           = "DDBv1_Error_Filter_US_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Critical, US DDBv1 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "us_ddbv1_critical_error_alarm_${var.environment}"
    Country  = "US"
    Type     = "DDBv1_Error"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv1_critical_error_us]
}

# =============================================================================
# DDBV1 US DATABASE ERROR MONITORING - WARNING
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv1_warning_error_us" {
  name           = "DDBv1_Warning_Error_MetricFilter_US"
  pattern        = "ERROR DDB"
  log_group_name = "US_DDBv1FileCreation_${var.environment}"

  metric_transformation {
    name      = "DDBv1_Error_Warning_Filter_US_${var.environment}"
    namespace = "DDBv1_Error_Warning_Filter_US_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv1_warning_error_us" {
  alarm_name          = "us_ddbv1_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv1_Error_Warning_Filter_US_${var.environment}"
  namespace           = "DDBv1_Error_Warning_Filter_US_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Warning, US DDBv1 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "us_ddbv1_warning_error_alarm_${var.environment}"
    Country  = "US"
    Type     = "DDBv1_Error"
    Severity = "Warning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv1_warning_error_us]
}

# =============================================================================
# DDBV2 US DATABASE ERROR MONITORING - CRITICAL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_critical_error_us" {
  name           = "DDBv2_Error_MetricFilter_US"
  pattern        = "?\"ERROR homenet\" ?\"DDB.SQL\""
  log_group_name = "US_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_US_${var.environment}"
    namespace = "DDBv2_Error_Filter_US_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_critical_error_us" {
  alarm_name          = "us_ddbv2_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2_Error_Filter_US_${var.environment}"
  namespace           = "DDBv2_Error_Filter_US_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Critical, US DDBv2 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "us_ddbv2_critical_error_alarm_${var.environment}"
    Country  = "US"
    Type     = "DDBv2_Error"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_critical_error_us]
}

# =============================================================================
# DDBV2 US DATABASE ERROR MONITORING - WARNING
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_warning_error_us" {
  name           = "DDBv2_Error_Warning_MetricFilter_US"
  pattern        = "ERROR DDB"
  log_group_name = "US_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_Warning_US_${var.environment}"
    namespace = "DDBv2_Error_Filter_Warning_US_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_warning_error_us" {
  alarm_name          = "us_ddbv2_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2_Error_Filter_Warning_US_${var.environment}"
  namespace           = "DDBv2_Error_Filter_Warning_US_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Warning, US DDBv2 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "us_ddbv2_warning_error_alarm_${var.environment}"
    Country  = "US"
    Type     = "DDBv2_Error"
    Severity = "Warning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_warning_error_us]
}

# =============================================================================
# DDBV2 CA DATABASE ERROR MONITORING - CRITICAL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_critical_error_ca" {
  name           = "DDBv2_Error_MetricFilter_CA"
  pattern        = "?\"ERROR homenet\" ?\"DDB.SQL\""
  log_group_name = "CA_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_CA_${var.environment}"
    namespace = "DDBv2_Error_Filter_CA_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_critical_error_ca" {
  alarm_name          = "ca_ddbv2_critical_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2_Error_Filter_CA_${var.environment}"
  namespace           = "DDBv2_Error_Filter_CA_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Critical, CA DDBv2 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "ca_ddbv2_critical_error_alarm_${var.environment}"
    Country  = "CA"
    Type     = "DDBv2_Error"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_critical_error_ca]
}

# =============================================================================
# DDBV2 CA DATABASE ERROR MONITORING - WARNING
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_warning_error_ca" {
  name           = "DDBv2_Error_Warning_MetricFilter_CA"
  pattern        = "ERROR DDB"
  log_group_name = "CA_DDBv2FileGeneration_${var.environment}"

  metric_transformation {
    name      = "DDBv2_Error_Filter_Warning_CA_${var.environment}"
    namespace = "DDBv2_Error_Filter_Warning_CA_Logs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_warning_error_ca" {
  alarm_name          = "ca_ddbv2_warning_error_alarm_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2_Error_Filter_Warning_CA_${var.environment}"
  namespace           = "DDBv2_Error_Filter_Warning_CA_Logs"
  period              = 300
  statistic           = "Sum"
  threshold           = 1
  alarm_description   = "Warning, CA DDBv2 has produced an Error"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "ca_ddbv2_warning_error_alarm_${var.environment}"
    Country  = "CA"
    Type     = "DDBv2_Error"
    Severity = "Warning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_warning_error_ca]
}
