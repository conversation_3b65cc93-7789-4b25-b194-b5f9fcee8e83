# =============================================================================
# REQUIRED VARIABLES
# =============================================================================

variable "application" {
  description = "The name of the application"
  type        = string
  
  validation {
    condition     = length(var.application) > 0
    error_message = "Application name cannot be empty."
  }
}

variable "environment" {
  description = "The deployment environment"
  type        = string
  
  validation {
    condition     = contains(["nonprod", "prod", "qamain", "qarapid", "uat", "scratch", "homenet"], var.environment)
    error_message = "Environment must be one of: nonprod, prod, qamain, qarapid, uat, scratch, homenet."
  }
}

variable "region" {
  description = "The AWS region"
  type        = string
  
  validation {
    condition     = can(regex("^[a-z]{2}-[a-z]+-[0-9]$", var.region))
    error_message = "Region must be a valid AWS region format (e.g., us-east-1)."
  }
}

# =============================================================================
# SNS TOPIC VARIABLES
# =============================================================================

variable "warning_alert_arn" {
  description = "The ARN of the SNS topic for warning alerts"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:sns:[a-z0-9-]+:[0-9]{12}:.+", var.warning_alert_arn))
    error_message = "Warning alert ARN must be a valid SNS topic ARN."
  }
}

variable "critical_alert_arn" {
  description = "The ARN of the SNS topic for critical alerts"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:sns:[a-z0-9-]+:[0-9]{12}:.+", var.critical_alert_arn))
    error_message = "Critical alert ARN must be a valid SNS topic ARN."
  }
}

variable "rrri_critical_alert_arn" {
  description = "The ARN of the SNS topic for RRRI critical alerts"
  type        = string
  
  validation {
    condition     = can(regex("^arn:aws:sns:[a-z0-9-]+:[0-9]{12}:.+", var.rrri_critical_alert_arn))
    error_message = "RRRI critical alert ARN must be a valid SNS topic ARN."
  }
}

# =============================================================================
# ALARM THRESHOLD VARIABLES
# =============================================================================

variable "ddbv2_long_running_oem_warning" {
  description = "Threshold for DDBv2 long running OEM warning alarm"
  type        = number
  default     = 5
  
  validation {
    condition     = var.ddbv2_long_running_oem_warning > 0
    error_message = "DDBv2 long running OEM warning threshold must be greater than 0."
  }
}

variable "rrri_long_running_oem_warning" {
  description = "Threshold for RRRI long running OEM warning alarm"
  type        = number
  default     = 3
  
  validation {
    condition     = var.rrri_long_running_oem_warning > 0
    error_message = "RRRI long running OEM warning threshold must be greater than 0."
  }
}

variable "vin_process_alarm_warning" {
  description = "Threshold for VIN processing warning alarm"
  type        = number
  default     = 100
  
  validation {
    condition     = var.vin_process_alarm_warning > 0
    error_message = "VIN process alarm warning threshold must be greater than 0."
  }
}

variable "vin_process_alarm_critical" {
  description = "Threshold for VIN processing critical alarm"
  type        = number
  default     = 500
  
  validation {
    condition     = var.vin_process_alarm_critical > 0
    error_message = "VIN process alarm critical threshold must be greater than 0."
  }
}

variable "ftp_alarm_threshold" {
  description = "Threshold for FTP retry exceeded alarm"
  type        = number
  default     = 1
  
  validation {
    condition     = var.ftp_alarm_threshold > 0
    error_message = "FTP alarm threshold must be greater than 0."
  }
}

variable "ftp_alarm_period" {
  description = "Period in seconds for FTP alarm evaluation"
  type        = number
  default     = 300
  
  validation {
    condition     = var.ftp_alarm_period > 0
    error_message = "FTP alarm period must be greater than 0."
  }
}

variable "ftp_alarm_evaluation_periods" {
  description = "Number of evaluation periods for FTP alarm"
  type        = number
  default     = 1
  
  validation {
    condition     = var.ftp_alarm_evaluation_periods > 0
    error_message = "FTP alarm evaluation periods must be greater than 0."
  }
}

# =============================================================================
# METADATA VARIABLES
# =============================================================================

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this infrastructure was launched"
  type        = string
  default     = ""
}

variable "slack_contact" {
  description = "Slack contact for this infrastructure"
  type        = string
  default     = ""
}

variable "build_number" {
  description = "The build number for this deployment"
  type        = string
  default     = "latest"
}

# =============================================================================
# TAGS VARIABLE
# =============================================================================

variable "tags" {
  description = "Additional tags to apply to all resources"
  type        = map(string)
  default     = {}
}
