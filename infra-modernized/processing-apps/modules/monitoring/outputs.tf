# =============================================================================
# DDBV2 LONG RUNNING OEMS OUTPUTS
# =============================================================================

output "ddbv2_long_running_us_alarm_arn" {
  description = "ARN of the US DDBv2 long running OEMs alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_long_running_us.arn
}

output "ddbv2_long_running_ca_alarm_arn" {
  description = "ARN of the CA DDBv2 long running OEMs alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_long_running_ca.arn
}

# =============================================================================
# RRRI LONG RUNNING OEMS OUTPUTS
# =============================================================================

output "rrri_long_running_us_alarm_arn" {
  description = "ARN of the US RRRI long running OEMs alarm"
  value       = aws_cloudwatch_metric_alarm.rrri_long_running_us.arn
}

output "rrri_long_running_ca_alarm_arn" {
  description = "ARN of the CA RRRI long running OEMs alarm"
  value       = aws_cloudwatch_metric_alarm.rrri_long_running_ca.arn
}

# =============================================================================
# VIN PROCESSING WARNING OUTPUTS
# =============================================================================

output "vin_processing_warning_us_alarm_arn" {
  description = "ARN of the US VIN processing warning alarm"
  value       = aws_cloudwatch_metric_alarm.vin_processing_warning_us.arn
}

output "vin_processing_warning_ca_alarm_arn" {
  description = "ARN of the CA VIN processing warning alarm"
  value       = aws_cloudwatch_metric_alarm.vin_processing_warning_ca.arn
}

# =============================================================================
# VIN PROCESSING CRITICAL OUTPUTS
# =============================================================================

output "vin_processing_critical_us_alarm_arn" {
  description = "ARN of the US VIN processing critical alarm"
  value       = aws_cloudwatch_metric_alarm.vin_processing_critical_us.arn
}

output "vin_processing_critical_ca_alarm_arn" {
  description = "ARN of the CA VIN processing critical alarm"
  value       = aws_cloudwatch_metric_alarm.vin_processing_critical_ca.arn
}

# =============================================================================
# DATABASE ERROR OUTPUTS
# =============================================================================

output "ddbv1_critical_error_us_alarm_arn" {
  description = "ARN of the US DDBv1 critical error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv1_critical_error_us.arn
}

output "ddbv1_warning_error_us_alarm_arn" {
  description = "ARN of the US DDBv1 warning error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv1_warning_error_us.arn
}

output "ddbv2_critical_error_us_alarm_arn" {
  description = "ARN of the US DDBv2 critical error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_critical_error_us.arn
}

output "ddbv2_warning_error_us_alarm_arn" {
  description = "ARN of the US DDBv2 warning error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_warning_error_us.arn
}

output "ddbv2_critical_error_ca_alarm_arn" {
  description = "ARN of the CA DDBv2 critical error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_critical_error_ca.arn
}

output "ddbv2_warning_error_ca_alarm_arn" {
  description = "ARN of the CA DDBv2 warning error alarm"
  value       = aws_cloudwatch_metric_alarm.ddbv2_warning_error_ca.arn
}

# =============================================================================
# FTP MONITORING OUTPUTS
# =============================================================================

output "ftp_retry_exceeded_small_alarm_arn" {
  description = "ARN of the FTP retry exceeded small division alarm"
  value       = aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_small.arn
}

output "ftp_retry_exceeded_medium_alarm_arn" {
  description = "ARN of the FTP retry exceeded medium division alarm"
  value       = aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_medium.arn
}

output "ftp_retry_exceeded_large_alarm_arn" {
  description = "ARN of the FTP retry exceeded large division alarm"
  value       = aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_large.arn
}

# =============================================================================
# METRIC FILTER OUTPUTS
# =============================================================================

output "ddbv2_long_running_us_filter_name" {
  description = "Name of the US DDBv2 long running metric filter"
  value       = aws_cloudwatch_log_metric_filter.ddbv2_long_running_us.name
}

output "ddbv2_long_running_ca_filter_name" {
  description = "Name of the CA DDBv2 long running metric filter"
  value       = aws_cloudwatch_log_metric_filter.ddbv2_long_running_ca.name
}

output "rrri_long_running_us_filter_name" {
  description = "Name of the US RRRI long running metric filter"
  value       = aws_cloudwatch_log_metric_filter.rrri_long_running_us.name
}

output "rrri_long_running_ca_filter_name" {
  description = "Name of the CA RRRI long running metric filter"
  value       = aws_cloudwatch_log_metric_filter.rrri_long_running_ca.name
}

# =============================================================================
# SUMMARY OUTPUTS
# =============================================================================

output "total_alarms_created" {
  description = "Total number of CloudWatch alarms created"
  value = length([
    aws_cloudwatch_metric_alarm.ddbv2_long_running_us,
    aws_cloudwatch_metric_alarm.ddbv2_long_running_ca,
    aws_cloudwatch_metric_alarm.rrri_long_running_us,
    aws_cloudwatch_metric_alarm.rrri_long_running_ca,
    aws_cloudwatch_metric_alarm.vin_processing_warning_us,
    aws_cloudwatch_metric_alarm.vin_processing_warning_ca,
    aws_cloudwatch_metric_alarm.vin_processing_critical_us,
    aws_cloudwatch_metric_alarm.vin_processing_critical_ca,
    aws_cloudwatch_metric_alarm.ddbv1_critical_error_us,
    aws_cloudwatch_metric_alarm.ddbv1_warning_error_us,
    aws_cloudwatch_metric_alarm.ddbv2_critical_error_us,
    aws_cloudwatch_metric_alarm.ddbv2_warning_error_us,
    aws_cloudwatch_metric_alarm.ddbv2_critical_error_ca,
    aws_cloudwatch_metric_alarm.ddbv2_warning_error_ca,
    aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_small,
    aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_medium,
    aws_cloudwatch_metric_alarm.ftp_retry_exceeded_ddbv2_large
  ])
}

output "total_metric_filters_created" {
  description = "Total number of CloudWatch log metric filters created"
  value = length([
    aws_cloudwatch_log_metric_filter.ddbv2_long_running_us,
    aws_cloudwatch_log_metric_filter.ddbv2_long_running_ca,
    aws_cloudwatch_log_metric_filter.rrri_long_running_us,
    aws_cloudwatch_log_metric_filter.rrri_long_running_ca,
    aws_cloudwatch_log_metric_filter.vin_processing_warning_us,
    aws_cloudwatch_log_metric_filter.vin_processing_warning_ca,
    aws_cloudwatch_log_metric_filter.vin_processing_critical_us,
    aws_cloudwatch_log_metric_filter.vin_processing_critical_ca,
    aws_cloudwatch_log_metric_filter.ddbv1_critical_error_us,
    aws_cloudwatch_log_metric_filter.ddbv1_warning_error_us,
    aws_cloudwatch_log_metric_filter.ddbv2_critical_error_us,
    aws_cloudwatch_log_metric_filter.ddbv2_warning_error_us,
    aws_cloudwatch_log_metric_filter.ddbv2_critical_error_ca,
    aws_cloudwatch_log_metric_filter.ddbv2_warning_error_ca,
    aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_small,
    aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_medium,
    aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_large
  ])
}
