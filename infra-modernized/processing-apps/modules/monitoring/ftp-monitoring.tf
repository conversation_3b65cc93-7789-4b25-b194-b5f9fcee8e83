# =============================================================================
# FTP RETRY EXCEEDED MONITORING
# =============================================================================

# =============================================================================
# FTP RETRY EXCEEDED - DDBV2 SMALL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ftp_retry_exceeded_ddbv2_small" {
  name           = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-small"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_small_${var.environment}"

  metric_transformation {
    name      = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-small"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ftp_retry_exceeded_ddbv2_small" {
  alarm_name          = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-small"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.ftp_alarm_threshold} for ${var.ftp_alarm_evaluation_periods} period(s) of ${var.ftp_alarm_period} seconds."
  metric_name         = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-small"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.ftp_alarm_threshold
  period              = var.ftp_alarm_period
  evaluation_periods  = var.ftp_alarm_evaluation_periods
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-small"
    Type     = "FTP_Retry_Exceeded"
    Division = "Small"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_small]
}

# =============================================================================
# FTP RETRY EXCEEDED - DDBV2 MEDIUM
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ftp_retry_exceeded_ddbv2_medium" {
  name           = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-medium"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_medium_${var.environment}"

  metric_transformation {
    name      = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-medium"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ftp_retry_exceeded_ddbv2_medium" {
  alarm_name          = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-medium"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.ftp_alarm_threshold} for ${var.ftp_alarm_evaluation_periods} period(s) of ${var.ftp_alarm_period} seconds."
  metric_name         = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-medium"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.ftp_alarm_threshold
  period              = var.ftp_alarm_period
  evaluation_periods  = var.ftp_alarm_evaluation_periods
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-medium"
    Type     = "FTP_Retry_Exceeded"
    Division = "Medium"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_medium]
}

# =============================================================================
# FTP RETRY EXCEEDED - DDBV2 LARGE
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ftp_retry_exceeded_ddbv2_large" {
  name           = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-large"
  pattern        = "\"File retry count 5 for file\""
  log_group_name = "DDBv2ProcessDivision_large_${var.environment}"

  metric_transformation {
    name      = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-large"
    namespace = "ProcessingApps"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ftp_retry_exceeded_ddbv2_large" {
  alarm_name          = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-large"
  namespace           = "ProcessingApps"
  alarm_description   = "Triggered when FTP metrics reaches >= ${var.ftp_alarm_threshold} for ${var.ftp_alarm_evaluation_periods} period(s) of ${var.ftp_alarm_period} seconds."
  metric_name         = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-large"
  statistic           = "Sum"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  threshold           = var.ftp_alarm_threshold
  period              = var.ftp_alarm_period
  evaluation_periods  = var.ftp_alarm_evaluation_periods
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "${local.name_prefix}-FTP-retry-exceeded-DDBv2-large"
    Type     = "FTP_Retry_Exceeded"
    Division = "Large"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ftp_retry_exceeded_ddbv2_large]
}
