# =============================================================================
# LOCAL VALUES
# =============================================================================

locals {
  # Common naming convention
  name_prefix = "${var.application}-${var.environment}"
  
  # Common tags applied to all resources
  common_tags = merge(var.tags, {
    Application  = var.application
    Environment  = var.environment
    Release      = var.build_number
    LaunchedBy   = var.launched_by
    LaunchedOn   = var.launched_on
    SlackContact = var.slack_contact
  })
}

# =============================================================================
# DDBV2 LONG RUNNING OEMS MONITORING - US
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_long_running_us" {
  name           = "DDBv2Log_MetricFilter_US"
  pattern        = "DDBv2LongRunningOEMsDetected"
  log_group_name = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "DDBv2LogFilter_US_${var.environment}"
    namespace = "US_DDBv2_Long_RunningLogs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_long_running_us" {
  alarm_name          = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2LogFilter_US_${var.environment}"
  namespace           = "US_DDBv2_Long_RunningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.ddbv2_long_running_oem_warning
  alarm_description   = "US Long Running OEMs greater than or equal to ${var.ddbv2_long_running_oem_warning} OEMs"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name    = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"
    Country = "US"
    Type    = "DDBv2_LongRunning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_long_running_us]
}

# =============================================================================
# DDBV2 LONG RUNNING OEMS MONITORING - CA
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "ddbv2_long_running_ca" {
  name           = "DDBv2Log_MetricFilter_CA"
  pattern        = "DDBv2LongRunningOEMsDetected"
  log_group_name = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "DDBv2LogFilter_CA_${var.environment}"
    namespace = "CA_DDBv2_Long_RunningLogs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "ddbv2_long_running_ca" {
  alarm_name          = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "DDBv2LogFilter_CA_${var.environment}"
  namespace           = "CA_DDBv2_Long_RunningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.ddbv2_long_running_oem_warning
  alarm_description   = "CA Long Running OEMs greater than or equal to ${var.ddbv2_long_running_oem_warning} OEMs"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name    = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"
    Country = "CA"
    Type    = "DDBv2_LongRunning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.ddbv2_long_running_ca]
}

# =============================================================================
# RRRI LONG RUNNING OEMS MONITORING - US
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "rrri_long_running_us" {
  name           = "RRRILog_MetricFilter_US"
  pattern        = "RRRILongRunningOEMsDetected"
  log_group_name = "US_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "RRRILogFilter_US_${var.environment}"
    namespace = "US_RRRI_Long_RunningLogs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "rrri_long_running_us" {
  alarm_name          = "US_RRRI_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RRRILogFilter_US_${var.environment}"
  namespace           = "US_RRRI_Long_RunningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.rrri_long_running_oem_warning
  alarm_description   = "US Long Running OEMs greater than or equal to ${var.rrri_long_running_oem_warning} OEMs"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.rrri_critical_alert_arn]
  ok_actions    = [var.rrri_critical_alert_arn]

  tags = merge(local.common_tags, {
    Name    = "US_RRRI_Check_LongRunning_OEMs_${var.environment}"
    Country = "US"
    Type    = "RRRI_LongRunning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.rrri_long_running_us]
}

# =============================================================================
# RRRI LONG RUNNING OEMS MONITORING - CA
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "rrri_long_running_ca" {
  name           = "RRRILog_MetricFilter_CA"
  pattern        = "RRRILongRunningOEMsDetected"
  log_group_name = "CA_DDBv2_Check_LongRunning_OEMs_${var.environment}"

  metric_transformation {
    name      = "RRRILogFilter_CA_${var.environment}"
    namespace = "CA_RRRI_Long_RunningLogs"
    value     = "1"
  }
}

resource "aws_cloudwatch_metric_alarm" "rrri_long_running_ca" {
  alarm_name          = "CA_RRRI_Check_LongRunning_OEMs_${var.environment}"
  comparison_operator = "GreaterThanOrEqualToThreshold"
  evaluation_periods  = 1
  metric_name         = "RRRILogFilter_CA_${var.environment}"
  namespace           = "CA_RRRI_Long_RunningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.rrri_long_running_oem_warning
  alarm_description   = "CA Long Running OEMs greater than or equal to ${var.rrri_long_running_oem_warning} OEMs"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.rrri_critical_alert_arn]
  ok_actions    = [var.rrri_critical_alert_arn]

  tags = merge(local.common_tags, {
    Name    = "CA_RRRI_Check_LongRunning_OEMs_${var.environment}"
    Country = "CA"
    Type    = "RRRI_LongRunning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.rrri_long_running_ca]
}

# =============================================================================
# VIN PROCESSING MONITORING - US WARNING
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "vin_processing_warning_us" {
  name           = "VinProcessLog_MetricFilter_US"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "US_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogWarningFilter_US_${var.environment}"
    namespace = "US_VIN_Processing_WarningLogs"
    value     = "$number"
  }
}

resource "aws_cloudwatch_metric_alarm" "vin_processing_warning_us" {
  alarm_name          = "US_Check_Vin_Process_Warning_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "VinProcessLogWarningFilter_US_${var.environment}"
  namespace           = "US_VIN_Processing_WarningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_warning
  alarm_description   = "Warning, Number US of Pending Vins over ${var.vin_process_alarm_warning}"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "US_Check_Vin_Process_Warning_${var.environment}"
    Country  = "US"
    Type     = "VIN_Processing_Warning"
    Severity = "Warning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.vin_processing_warning_us]
}

# =============================================================================
# VIN PROCESSING MONITORING - CA WARNING
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "vin_processing_warning_ca" {
  name           = "VinProcessLog_MetricFilter_CA"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "CA_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogWarningFilter_CA_${var.environment}"
    namespace = "CA_VIN_Processing_WarningLogs"
    value     = "$number"
  }
}

resource "aws_cloudwatch_metric_alarm" "vin_processing_warning_ca" {
  alarm_name          = "CA_Check_Vin_Process_Warning_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "VinProcessLogWarningFilter_CA_${var.environment}"
  namespace           = "CA_VIN_Processing_WarningLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_warning
  alarm_description   = "Warning, Number CA of Pending Vins over ${var.vin_process_alarm_warning}"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.warning_alert_arn]
  ok_actions    = [var.warning_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "CA_Check_Vin_Process_Warning_${var.environment}"
    Country  = "CA"
    Type     = "VIN_Processing_Warning"
    Severity = "Warning"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.vin_processing_warning_ca]
}

# =============================================================================
# VIN PROCESSING MONITORING - US CRITICAL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "vin_processing_critical_us" {
  name           = "VinProcessLog_CriticalMetricFilter_US"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "US_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogCriticalFilter_US_${var.environment}"
    namespace = "US_VIN_Processing_CriticalLogs"
    value     = "$number"
  }
}

resource "aws_cloudwatch_metric_alarm" "vin_processing_critical_us" {
  alarm_name          = "US_Check_Vin_Process_Critical_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "VinProcessLogCriticalFilter_US_${var.environment}"
  namespace           = "US_VIN_Processing_CriticalLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_critical
  alarm_description   = "Critical, Number US of Pending Vins over ${var.vin_process_alarm_critical}"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "US_Check_Vin_Process_Critical_${var.environment}"
    Country  = "US"
    Type     = "VIN_Processing_Critical"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.vin_processing_critical_us]
}

# =============================================================================
# VIN PROCESSING MONITORING - CA CRITICAL
# =============================================================================

resource "aws_cloudwatch_log_metric_filter" "vin_processing_critical_ca" {
  name           = "VinProcessLog_CriticalMetricFilter_CA"
  pattern        = "[text = NumberVINsInactive, number > 0]"
  log_group_name = "CA_Check_VIN_Processing_${var.environment}"

  metric_transformation {
    name      = "VinProcessLogCriticalFilter_CA_${var.environment}"
    namespace = "CA_VIN_Processing_CriticalLogs"
    value     = "$number"
  }
}

resource "aws_cloudwatch_metric_alarm" "vin_processing_critical_ca" {
  alarm_name          = "CA_Check_Vin_Process_Critical_${var.environment}"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = 1
  metric_name         = "VinProcessLogCriticalFilter_CA_${var.environment}"
  namespace           = "CA_VIN_Processing_CriticalLogs"
  period              = 300
  statistic           = "Sum"
  threshold           = var.vin_process_alarm_critical
  alarm_description   = "Critical, Number CA of Pending Vins over ${var.vin_process_alarm_critical}"
  treat_missing_data  = "notBreaching"

  alarm_actions = [var.critical_alert_arn]
  ok_actions    = [var.critical_alert_arn]

  tags = merge(local.common_tags, {
    Name     = "CA_Check_Vin_Process_Critical_${var.environment}"
    Country  = "CA"
    Type     = "VIN_Processing_Critical"
    Severity = "Critical"
  })

  depends_on = [aws_cloudwatch_log_metric_filter.vin_processing_critical_ca]
}
