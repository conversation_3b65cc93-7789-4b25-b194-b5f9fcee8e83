# =============================================================================
# ENVIRONMENT VARIABLES
# =============================================================================

variable "region" {
  description = "The AWS region to deploy resources"
  type        = string
  default     = "us-east-1"
}

variable "environment" {
  description = "The deployment environment"
  type        = string
  default     = "nonprod"
}

variable "application" {
  description = "The application name"
  type        = string
  default     = "ais"
}

variable "application_abbreviated" {
  description = "The abbreviated application name"
  type        = string
  default     = "ais"
}

variable "service" {
  description = "The service name"
  type        = string
  default     = "processing-apps"
}

variable "component" {
  description = "The component name"
  type        = string
  default     = "processing-apps"
}

# =============================================================================
# COMPONENT IDS
# =============================================================================

variable "processing_apps_component_id" {
  description = "The Component ID of the processing apps"
  type        = string
  default     = "CI0934608"
}

# =============================================================================
# NETWORKING VARIABLES
# =============================================================================

variable "vpc_name" {
  description = "The name of the VPC"
  type        = string
  default     = "awsaaia"
}

variable "regions_abbreviated" {
  description = "Map of region abbreviations"
  type        = map(string)
  default = {
    "us-east-1" = "ue1"
    "us-west-2" = "uw2"
  }
}

variable "availability_zones" {
  description = "List of availability zones"
  type        = list(string)
  default     = ["us-east-1a", "us-east-1b", "us-east-1c"]
}

variable "homenet_cidr" {
  description = "CIDR block for homenet access"
  type        = string
  default     = "10.0.0.0/8"
}

variable "ais_cidr" {
  description = "CIDR block for AIS network"
  type        = string
  default     = "**********/12"
}

variable "remote_cidr" {
  description = "CIDR block for remote access"
  type        = string
  default     = "***********/16"
}

variable "nfs_cidr" {
  description = "CIDR block for NFS access"
  type        = string
  default     = "***********/22"
}

# =============================================================================
# EFS CONFIGURATION
# =============================================================================

variable "efs_shares" {
  description = "EFS file system IDs by region"
  type        = map(string)
  default = {
    "us-east-1" = "fs-5fcfafaa"
  }
}

variable "efs_security_group" {
  description = "Security group IDs for EFS access by region"
  type        = map(string)
  default = {
    "us-east-1" = "sg-12345678"
  }
}

# =============================================================================
# AMI CONFIGURATION
# =============================================================================

variable "ecs_ami_ids" {
  description = "ECS-optimized AMI IDs by region"
  type        = map(string)
  default = {
    "us-east-1" = "ami-0c02fb55956c7d316"  # This should be updated to actual ECS-optimized AMI
  }
}

# =============================================================================
# S3 CONFIGURATION
# =============================================================================

variable "package_bucket_names" {
  description = "S3 bucket names for packages by region"
  type        = map(string)
  default = {
    "us-east-1" = "ais.1-0.application.packages.np.ue1"
  }
}

# =============================================================================
# LAMBDA CONFIGURATION
# =============================================================================

variable "package_path_ecs_termination_protection" {
  description = "Path to the ECS termination protection Lambda package"
  type        = string
  # This should be provided via terraform.tfvars or environment variables
}

variable "account_type" {
  description = "Account type (nonprod or prod)"
  type        = string
  default     = "nonprod"
}

variable "account_id" {
  description = "AWS Account ID"
  type        = string
  # This should be provided via terraform.tfvars or environment variables
}

# =============================================================================
# AUTO SCALING CONFIGURATION
# =============================================================================

variable "autoscale_scheduling_normal_map" {
  description = "Auto scaling schedule map for normal business hours"
  type        = map(string)
  default = {
    "default.morning" = "0 12 * * MON-FRI"
    "default.night"   = "0 2 * * TUE-SAT"
    "nonprod.morning" = "0 12 * * MON-FRI"
    "nonprod.night"   = "0 2 * * TUE-SAT"
  }
}

# =============================================================================
# METADATA VARIABLES
# =============================================================================

variable "launched_by" {
  description = "Who launched this infrastructure"
  type        = string
  default     = "terraform"
}

variable "launched_on" {
  description = "When this infrastructure was launched"
  type        = string
  default     = ""
}

variable "slack_contact" {
  description = "Slack contact for this infrastructure"
  type        = string
  default     = ""
}

variable "build_number" {
  description = "Build number for this deployment"
  type        = string
  default     = "latest"
}
