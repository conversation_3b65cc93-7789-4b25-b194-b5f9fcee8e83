# =============================================================================
# NONPROD ENVIRONMENT CONFIGURATION
# =============================================================================

terraform {
  required_version = ">= 1.6.0"

  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }

  backend "s3" {
    # Backend configuration should be provided via backend config file
    # or command line arguments during terraform init
  }
}

# =============================================================================
# PROVIDER CONFIGURATION
# =============================================================================

provider "aws" {
  region = var.region

  default_tags {
    tags = {
      "coxauto:ci-id" = var.processing_apps_component_id
      Environment     = var.environment
      ManagedBy      = "terraform"
      Project        = "AIS-1.0"
    }
  }

  ignore_tags {
    key_prefixes = ["cai:catalog"]
  }
}

# =============================================================================
# LOCAL VALUES
# =============================================================================

locals {
  # Common tags for all resources
  common_tags = {
    Application     = var.application
    Environment     = var.environment
    Service         = var.service
    Region          = var.region
    LaunchedBy      = var.launched_by
    LaunchedOn      = var.launched_on
    SlackContact    = var.slack_contact
    BuildNumber     = var.build_number
    "coxauto:ci-id" = var.processing_apps_component_id
  }

  # Environment-specific configuration
  environment_config = {
    min_size         = 3
    max_size         = 18
    desired_capacity = 3
    instance_type    = "m5.4xlarge"
  }
}

# =============================================================================
# DATA SOURCES
# =============================================================================

data "aws_vpc" "main" {
  filter {
    name   = "tag:Name"
    values = [var.vpc_name]
  }
}

data "aws_subnets" "private" {
  filter {
    name   = "vpc-id"
    values = [data.aws_vpc.main.id]
  }

  filter {
    name   = "tag:Type"
    values = ["Private"]
  }
}

data "aws_ami" "ecs_optimized" {
  most_recent = true
  owners      = ["amazon"]

  filter {
    name   = "name"
    values = ["amzn2-ami-ecs-hvm-*-x86_64-ebs"]
  }

  filter {
    name   = "virtualization-type"
    values = ["hvm"]
  }
}

# =============================================================================
# ECS CLUSTER MODULE
# =============================================================================

module "ecs_cluster" {
  source = "../../modules/ecs-cluster"

  # Required variables
  application             = var.application
  application_abbreviated = var.application_abbreviated
  service                = var.service
  region                 = var.region
  region_abbreviated     = var.regions_abbreviated[var.region]
  environment            = var.environment
  component              = var.component
  component_id           = var.processing_apps_component_id

  # Networking
  vpc_id             = data.aws_vpc.main.id
  private_subnet_ids = data.aws_subnets.private.ids
  availability_zones = var.availability_zones
  homenet_cidr      = var.homenet_cidr
  ais_cidr          = var.ais_cidr
  remote_cidr       = var.remote_cidr
  nfs_cidr          = var.nfs_cidr

  # EFS and Security
  efs_id         = var.efs_shares[var.region]
  security_group = var.efs_security_group[var.region]

  # Instance Configuration
  ami_id        = var.ecs_ami_ids[var.region]
  instance_type = local.environment_config.instance_type

  # Auto Scaling
  min_size         = local.environment_config.min_size
  max_size         = local.environment_config.max_size
  desired_capacity = local.environment_config.desired_capacity

  # Scheduling
  scheduling_enabled         = true
  scheduling_normal_map      = var.autoscale_scheduling_normal_map
  extended_scheduling_enabled = false

  # Lambda Configuration
  package_path_ecs_termination_protection = var.package_path_ecs_termination_protection
  account_type                           = var.account_type

  # Metadata
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  build_number  = var.build_number

  tags = local.common_tags
}

# =============================================================================
# MONITORING MODULE
# =============================================================================

module "monitoring" {
  source = "../../modules/monitoring"

  # Required variables
  application = var.application
  environment = var.environment
  region      = var.region

  # SNS Topics (from remote state)
  warning_alert_arn      = data.terraform_remote_state.sns_alerts.outputs.warning_alert_arn
  critical_alert_arn     = data.terraform_remote_state.sns_alerts.outputs.critical_alert_arn
  rrri_critical_alert_arn = data.terraform_remote_state.sns_alerts.outputs.rrri_critical_alert_arn

  # Alarm Thresholds
  ddbv2_long_running_oem_warning = 5
  rrri_long_running_oem_warning  = 3
  vin_process_alarm_warning      = 100
  vin_process_alarm_critical     = 500

  # Metadata
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  build_number  = var.build_number

  tags = local.common_tags
}

# =============================================================================
# REMOTE STATE DATA SOURCES
# =============================================================================

data "terraform_remote_state" "sns_alerts" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/nonprod/sns-alerts"
    region = var.region
  }
}
