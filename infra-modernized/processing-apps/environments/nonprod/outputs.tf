# =============================================================================
# ECS CLUSTER OUTPUTS
# =============================================================================

output "cluster_id" {
  description = "The ID of the ECS cluster"
  value       = module.ecs_cluster.cluster_id
}

output "cluster_arn" {
  description = "The ARN of the ECS cluster"
  value       = module.ecs_cluster.cluster_arn
}

output "cluster_name" {
  description = "The name of the ECS cluster"
  value       = module.ecs_cluster.cluster_name
}

# =============================================================================
# AUTO SCALING GROUP OUTPUTS
# =============================================================================

output "autoscaling_group_id" {
  description = "The ID of the Auto Scaling Group"
  value       = module.ecs_cluster.autoscaling_group_id
}

output "autoscaling_group_arn" {
  description = "The ARN of the Auto Scaling Group"
  value       = module.ecs_cluster.autoscaling_group_arn
}

output "autoscaling_group_name" {
  description = "The name of the Auto Scaling Group"
  value       = module.ecs_cluster.autoscaling_group_name
}

# =============================================================================
# SECURITY GROUP OUTPUTS
# =============================================================================

output "security_group_id" {
  description = "The ID of the ECS instances security group"
  value       = module.ecs_cluster.security_group_id
}

# =============================================================================
# LAMBDA FUNCTION OUTPUTS
# =============================================================================

output "lambda_function_arn" {
  description = "The ARN of the ECS termination protection Lambda function"
  value       = module.ecs_cluster.lambda_function_arn
}

output "lambda_function_name" {
  description = "The name of the ECS termination protection Lambda function"
  value       = module.ecs_cluster.lambda_function_name
}

# =============================================================================
# MONITORING OUTPUTS
# =============================================================================

output "total_alarms_created" {
  description = "Total number of CloudWatch alarms created"
  value       = module.monitoring.total_alarms_created
}

output "total_metric_filters_created" {
  description = "Total number of CloudWatch log metric filters created"
  value       = module.monitoring.total_metric_filters_created
}

# =============================================================================
# INFRASTRUCTURE SUMMARY
# =============================================================================

output "infrastructure_summary" {
  description = "Summary of the deployed infrastructure"
  value = {
    environment           = var.environment
    region               = var.region
    cluster_name         = module.ecs_cluster.cluster_name
    autoscaling_group    = module.ecs_cluster.autoscaling_group_name
    lambda_function      = module.ecs_cluster.lambda_function_name
    total_alarms         = module.monitoring.total_alarms_created
    total_metric_filters = module.monitoring.total_metric_filters_created
    deployment_time      = timestamp()
  }
}
