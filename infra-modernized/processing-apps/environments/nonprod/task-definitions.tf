# =============================================================================
# TASK DEFINITIONS FOR NONPROD ENVIRONMENT
# =============================================================================

# =============================================================================
# US AUDIT DATA POINTS TASK
# =============================================================================

module "us_audit_data_points" {
  source = "../../modules/task-definitions"

  # Required variables
  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  component     = var.component

  # Task configuration
  country_iso_code          = "US"
  task_friendly_name        = "US_AuditDataPoints"
  container_definition_path = "../../modules/task-definitions/container-definitions/US_AuditDataPoints.json"
  
  # IAM roles
  task_role_arn      = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  
  # Container configuration
  image_url_name_tag = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  cpu                = 512
  memory             = 512
  
  # S3 and SNS configuration
  ini_bucket     = var.package_bucket_names[var.region]
  rrri_topic_arn = data.terraform_remote_state.rrri.outputs.topic_arn
  
  # Scheduling
  schedule_expression = "0 12,18,03 * * ? *"  # Run at 8am, 2pm & 10pm EST
  enabled            = true
  task_count         = 1
  
  # Logging
  retention_in_days = 7

  # Metadata
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  build_number  = var.build_number

  tags = local.common_tags
}

# =============================================================================
# CA AUDIT DATA POINTS TASK
# =============================================================================

module "ca_audit_data_points" {
  source = "../../modules/task-definitions"

  # Required variables
  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  component     = var.component

  # Task configuration
  country_iso_code          = "CA"
  task_friendly_name        = "CA_AuditDataPoints"
  container_definition_path = "../../modules/task-definitions/container-definitions/CA_AuditDataPoints.json"
  
  # IAM roles
  task_role_arn      = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  
  # Container configuration
  image_url_name_tag = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  cpu                = 512
  memory             = 512
  
  # S3 and SNS configuration
  ini_bucket     = var.package_bucket_names[var.region]
  rrri_topic_arn = data.terraform_remote_state.rrri.outputs.topic_arn
  
  # Scheduling
  schedule_expression = "0 12,18,03 * * ? *"  # Run at 8am, 2pm & 10pm EST
  enabled            = true
  task_count         = 1
  
  # Logging
  retention_in_days = 7

  # Metadata
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  build_number  = var.build_number

  tags = local.common_tags
}

# =============================================================================
# US DDBV2 FILE GENERATION TASK
# =============================================================================

module "us_ddbv2_file_generation" {
  source = "../../modules/task-definitions"

  # Required variables
  application   = var.application
  service       = var.service
  region        = var.region
  environment   = var.environment
  component     = var.component

  # Task configuration
  country_iso_code          = "US"
  task_friendly_name        = "US_DDBv2FileGeneration"
  container_definition_path = "../../modules/task-definitions/container-definitions/US_DDBv2FileGeneration.json"
  
  # IAM roles
  task_role_arn      = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  execution_role_arn = "arn:aws:iam::${var.account_id}:role/acct-managed/ais10-ecs-task-role"
  
  # Container configuration
  image_url_name_tag = "${var.account_id}.dkr.ecr.${var.region}.amazonaws.com/processingapps_${var.environment}:latest"
  cpu                = 1024
  memory             = 2048
  
  # S3 and SNS configuration
  ini_bucket     = var.package_bucket_names[var.region]
  rrri_topic_arn = data.terraform_remote_state.rrri.outputs.topic_arn
  
  # Scheduling
  schedule_expression = "0 6 * * ? *"  # Run at 2am EST daily
  enabled            = true
  task_count         = 1
  
  # Logging
  retention_in_days = 14

  # Metadata
  launched_by   = var.launched_by
  launched_on   = var.launched_on
  slack_contact = var.slack_contact
  build_number  = var.build_number

  tags = local.common_tags
}

# =============================================================================
# REMOTE STATE DATA SOURCES
# =============================================================================

data "terraform_remote_state" "rrri" {
  backend = "s3"

  config = {
    bucket = "ais.nonprod.${var.regions_abbreviated[var.region]}.infrastructure.tf.state"
    key    = "AIS-1.0/${var.environment}/rrri"
    region = var.region
  }
}
